# SlowQueryScoreConfig 配置类型功能修改总结

## 概述
根据您的需求，我们在 `rds_manager_slowqueryscoreconfig` 表中添加了 `config_type` 字段，用于区分慢查询和 SQL 模板的评分配置。现在系统支持两种配置类型：
- `slowlog`: 慢查询配置
- `sqltemplate`: SQL 模板配置

## 数据库修改

### 1. 字段添加
```sql
ALTER TABLE rds_manager_slowqueryscoreconfig 
ADD config_type VARCHAR(16) NULL COMMENT '配置类型' AFTER `name`;
```

### 2. 迁移文件
- 创建了迁移文件：`rds_manager/migrations/0005_alter_slowqueryscoreconfig_options_and_more.py`
- 已应用到数据库

## 模型修改 (rds_manager/models.py)

### 1. 添加配置类型选择
```python
CONFIG_TYPE_CHOICES = (
    ('slowlog', '慢查询'),
    ('sqltemplate', 'SQL模板'),
)

config_type = models.CharField(
    max_length=16, 
    choices=CONFIG_TYPE_CHOICES, 
    default='slowlog', 
    verbose_name="配置类型"
)
```

### 2. 更新 get_active_config 方法
```python
@staticmethod
def get_active_config(config_type='slowlog'):
    """获取指定类型的激活配置"""
    config = SlowQueryScoreConfig.objects.filter(
        is_active=True, 
        config_type=config_type
    ).first()
    # 如果没有配置，自动创建默认配置
```

### 3. 添加 save 方法重写
```python
def save(self, *args, **kwargs):
    """保存时确保同一类型只有一个激活配置"""
    if self.is_active:
        SlowQueryScoreConfig.objects.filter(
            config_type=self.config_type,
            is_active=True
        ).exclude(pk=self.pk).update(is_active=False)
    super().save(*args, **kwargs)
```

### 4. 更新 Meta 类和 __str__ 方法
- 更新了 verbose_name 为更通用的"评分配置"
- __str__ 方法现在显示配置类型信息

## 业务逻辑修改

### 1. api/services.py
- 更新 `analyze_sql` 方法使用 `config_type='sqltemplate'`
- 优先查找名为 "audit_score" 的 SQL 模板配置

### 2. rds_manager/services.py
- 更新 `analyze_slow_query` 方法使用 `config_type='slowlog'`

### 3. rds_manager/views.py
- 更新配置列表视图支持配置类型显示
- 更新配置创建/编辑视图支持配置类型字段
- 更新激活配置逻辑，确保同类型只有一个激活配置

## 模板修改

### 1. score_config_list.html
- 页面标题更新为"评分配置管理"
- 配置卡片显示配置类型徽章
- 区分慢查询(蓝色)和 SQL 模板(黄色)

### 2. score_config_form.html
- 添加配置类型选择下拉框
- 更新页面标题为更通用的"评分配置"
- 添加配置类型说明文字

## 功能特性

### 1. 配置类型隔离
- 慢查询和 SQL 模板使用独立的评分配置
- 每种类型可以有多个配置，但只能有一个激活配置

### 2. 自动配置管理
- 激活新配置时，自动将同类型的其他配置设为非激活
- 如果没有激活配置，系统会自动创建默认配置

### 3. 向后兼容
- 现有配置自动设置为 `slowlog` 类型
- 所有现有功能保持正常工作

## 测试验证

### 1. 功能测试
✅ 配置类型字段正常工作
✅ 获取不同类型的激活配置功能正常
✅ 激活配置唯一性约束正常工作
✅ 配置创建和编辑功能正常
✅ 模板页面正确显示配置类型信息

### 2. 数据验证
- 现有配置：
  - ID: 1, 名称: "日内分析", 类型: slowlog, 状态: 激活
  - ID: 2, 名称: "audit_score", 类型: sqltemplate, 状态: 激活

## 使用方法

### 1. 获取激活配置
```python
# 获取慢查询激活配置
slowlog_config = SlowQueryScoreConfig.get_active_config('slowlog')

# 获取 SQL 模板激活配置
sqltemplate_config = SlowQueryScoreConfig.get_active_config('sqltemplate')
```

### 2. 创建新配置
```python
config = SlowQueryScoreConfig.objects.create(
    name="新配置",
    config_type="slowlog",  # 或 "sqltemplate"
    description="配置描述",
    is_active=True,
    # ... 其他参数
)
```

## 注意事项

1. **配置类型不可随意更改**：一旦创建，建议不要更改配置的类型，因为这可能影响相关的分析结果。

2. **激活配置唯一性**：系统会自动确保同一类型只有一个激活配置，无需手动管理。

3. **默认配置**：如果某个类型没有激活配置，系统会自动创建默认配置。

4. **数据迁移**：现有的所有配置都被设置为 `slowlog` 类型，需要手动将 SQL 模板相关的配置改为 `sqltemplate` 类型。

## 完成状态
✅ 所有修改已完成并测试通过
✅ 数据库迁移已应用
✅ 功能测试验证通过
✅ 向后兼容性保持良好
