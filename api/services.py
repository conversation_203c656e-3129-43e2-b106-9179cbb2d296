import logging
from datetime import datetime, timedelta, date
from django.utils import timezone
from django.conf import settings
from loguru import logger
import json
import time
from cryptography.fernet import Fernet
from django.db import transaction
import re
import math

from rds_manager.models import RDSInstance, Database, DBAccount, InstanceAccessPermission, SlowQueryLog, SlowQueryScoreConfig
from sql_risk_analysis.models import SQLTemplate, SQLAuditScore
from utils.alicloud_utils import get_client
from utils.exceptions import AliCloudApiError, ServiceError

# 导入阿里云SDK相关模块
from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.acs_exception.exceptions import ClientException, ServerException
from aliyunsdkrds.request.v20140815.DescribeSlowLogRecordsRequest import DescribeSlowLogRecordsRequest
from aliyunsdkrds.request.v20140815.DescribeSlowLogsRequest import DescribeSlowLogsRequest
from aliyunsdkrds.request.v20140815.DescribeSQLLogRecordsRequest import DescribeSQLLogRecordsRequest
try:
    from aliyunsdkdas.request.v20200116.GetFullRequestStatResultByInstanceIdRequest import GetFullRequestStatResultByInstanceIdRequest
    from aliyunsdkdas.request.v20200116.GetFullRequestSampleByInstanceIdRequest import GetFullRequestSampleByInstanceIdRequest
    DAS_SDK_AVAILABLE = True
    print("DAS SDK导入成功")
except ImportError as e:
    print(f"DAS SDK导入失败: {e}")
    DAS_SDK_AVAILABLE = False
    # 创建占位符类
    class GetFullRequestStatResultByInstanceIdRequest:
        def __init__(self): pass
        def set_InstanceId(self, instance_id): pass
        def set_Start(self, start): pass
        def set_End(self, end): pass
        def set_PageNo(self, page_no): pass
        def set_PageSize(self, page_size): pass
        def set_SqlId(self, sql_id): pass
        def set_OrderBy(self, order_by): pass
        def set_Asc(self, asc): pass
        def set_SqlType(self, sql_type): pass
        def set_Keyword(self, keyword): pass

    class GetFullRequestSampleByInstanceIdRequest:
        def __init__(self): pass
        def set_InstanceId(self, instance_id): pass
        def set_SqlId(self, sql_id): pass
        def set_Start(self, start): pass
        def set_End(self, end): pass
from aliyunsdkrds.request.v20140815.DescribeDBInstancesRequest import DescribeDBInstancesRequest
from aliyunsdkrds.request.v20140815.DescribeDBInstanceAttributeRequest import DescribeDBInstanceAttributeRequest
from aliyunsdkrds.request.v20140815.DescribeDatabasesRequest import DescribeDatabasesRequest
from aliyunsdkrds.request.v20140815.DescribeAccountsRequest import DescribeAccountsRequest

# 配置日志
logging_client = logging.getLogger(__name__)

#######################################################
# 阿里云服务接口层 - 统一的API交互接口
#######################################################

class AliCloudService:
    """阿里云服务统一接口类，提供所有阿里云API访问能力"""
    
    def __init__(self, access_key=None, access_secret=None, region_id=None):
        """初始化阿里云服务客户端
        
        Args:
            access_key: 阿里云访问密钥ID，如果不提供则使用配置中的默认值
            access_secret: 阿里云访问密钥Secret，如果不提供则使用配置中的默认值
            region_id: 阿里云区域ID，如果不提供则使用配置中的默认值
        """
        self.client = get_client(access_key, access_secret, region_id)
        self.logger = logging.getLogger('api')
    
    def _make_request(self, request):
        """发送API请求并处理常见错误
        
        Args:
            request: 阿里云SDK请求对象
            
        Returns:
            dict: API响应结果
            
        Raises:
            AliCloudApiError: 当API调用失败时抛出
        """
        try:
            response = self.client.do_action_with_exception(request)
            return json.loads(response.decode('utf-8'))
        except ClientException as e:
            self.logger.error(f"ClientException in API call: {e}")
            raise AliCloudApiError(f"客户端错误: {e}")
        except ServerException as e:
            self.logger.error(f"ServerException in API call: {e}")
            raise AliCloudApiError(f"服务器错误: {e}")
        except Exception as e:
            self.logger.error(f"Unexpected error in API call: {e}")
            raise AliCloudApiError(f"API调用错误: {e}")

    #---------- RDS 实例相关 API 操作 ----------#
    
    def list_instances(self, page_size=100, page_number=1):
        """获取RDS实例列表
        
        Args:
            page_size: 每页记录数
            page_number: 页码
            
        Returns:
            dict: 包含实例列表的响应对象
        """
        request = DescribeDBInstancesRequest()
        request.set_PageSize(page_size)
        request.set_PageNumber(page_number)
        return self._make_request(request)
    
    def get_instance_details(self, instance_id):
        """获取RDS实例详情
        
        Args:
            instance_id: RDS实例ID
            
        Returns:
            dict: 包含实例详情的响应对象
        """
        request = DescribeDBInstanceAttributeRequest()
        request.set_DBInstanceId(instance_id)
        return self._make_request(request)
    
    def get_instance_databases(self, instance_id):
        """获取RDS实例的数据库列表
        
        Args:
            instance_id: RDS实例ID
            
        Returns:
            dict: 包含数据库列表的响应对象
        """
        request = DescribeDatabasesRequest()
        request.set_DBInstanceId(instance_id)
        return self._make_request(request)
    
    def get_instance_accounts(self, instance_id):
        """获取RDS实例的账号列表
        
        Args:
            instance_id: RDS实例ID
            
        Returns:
            dict: 包含账号列表的响应对象
        """
        request = DescribeAccountsRequest()
        request.set_DBInstanceId(instance_id)
        return self._make_request(request)
        
    #---------- SQL模板和慢查询相关 API 操作 ----------#
    
    def get_sql_templates(self, instance_id, date=None, sql_id=None, page_no=1, page_size=100, order_by="count", 
                          asc=False, sql_type=None, keyword=None):
        """获取SQL模板列表 - 按照SQL ID异步统计全量请求数据
        
        Args:
            instance_id: RDS实例ID
            date: 日期，格式为YYYY-MM-DD，默认为当天
            sql_id: SQL ID，可选，指定后只返回该SQL ID对应的统计数据
            page_no: 页码，从1开始，默认为1
            page_size: 每页记录数，默认为100
            order_by: 排序字段，可选值为count(执行次数), avgRt(平均执行时间), rtRate(耗时比例)等，默认为count
            asc: 是否按升序排序，默认为False
            sql_type: SQL类型，可选值为SELECT, INSERT, UPDATE, DELETE等
            keyword: 搜索关键词，可选
            
        Returns:
            dict: SQL模板响应对象
        """
        # 设置默认日期为当天
        if not date:
            date = timezone.now().strftime('%Y-%m-%d')

        self.logger.debug(f"传入参数 实例ID：{instance_id}||date:{date}||sql_id:{sql_id}||page_no:{page_no}||page_size:{page_size}||order_by:{order_by}||asc:{asc}||sql_type:{sql_type}||keyword:{keyword}")
        
        # 转换时间格式为unix时间戳(毫秒)
        start_time = int(datetime.strptime(f"{date} 00:00:00", '%Y-%m-%d %H:%M:%S').timestamp() * 1000)
        end_time = int(datetime.strptime(f"{date} 23:59:59", '%Y-%m-%d %H:%M:%S').timestamp() * 1000)
        
        # 准备请求
        request = GetFullRequestStatResultByInstanceIdRequest()
        request.set_InstanceId(instance_id)
        request.set_Start(start_time)  # 设置开始时间(毫秒级时间戳)
        request.set_End(end_time)      # 设置结束时间(毫秒级时间戳)
        request.set_PageNo(page_no)
        request.set_PageSize(page_size)
        request.set_OrderBy(order_by)
        request.set_Asc(asc)
        
        # 设置可选参数
        if sql_id:
            request.set_SqlId(sql_id)
        if sql_type:
            request.set_SqlType(sql_type)
        if keyword:
            request.set_Keyword(keyword)
        
        # 异步调用API并轮询结果
        max_retry = 10
        retry_count = 0
        last_response = None
        
        while retry_count < max_retry:
            # 每次都创建新的请求对象，避免修改之前的请求
            response = self._make_request(request)
            last_response = response
            
            # 检查请求是否完成
            data = response.get('Data', {})
            is_finish = data.get('IsFinish')
            
            if is_finish:
                # 请求完成，返回结果
                return response
            else:
                # 请求未完成，等待1秒后重试
                retry_count += 1
                time.sleep(1)
                
                # 对于后续请求，我们不设置ResultId，直接使用相同的参数重新请求
                # 阿里云API会自动关联之前的请求
        
        # 达到最大重试次数，返回最后一次请求的结果
        self.logger.warning(f"获取SQL模板达到最大重试次数({max_retry})，返回最后一次结果")
        return last_response
    
    def get_slow_query_logs(self, instance_id, start_time, end_time):
        """获取慢查询日志
        
        Args:
            instance_id: RDS实例ID
            start_time: 开始时间，格式为YYYY-MM-DD HH:MM:SS
            end_time: 结束时间，格式为YYYY-MM-DD HH:MM:SS
            
        Returns:
            dict: 慢查询日志响应对象
        """
        request = DescribeSlowLogsRequest()
        request.set_DBInstanceId(instance_id)
        request.set_StartTime(start_time)
        request.set_EndTime(end_time)
        
        return self._make_request(request)
    
    def get_slow_query_records(self, instance_id, start_time, end_time, db_name=None):
        """获取慢查询记录详情
        
        Args:
            instance_id: RDS实例ID
            start_time: 开始时间，格式为YYYY-MM-DD HH:MM:SS
            end_time: 结束时间，格式为YYYY-MM-DD HH:MM:SS
            db_name: 数据库名称，可选
            
        Returns:
            dict: 慢查询记录详情响应对象
        """
        request = DescribeSlowLogRecordsRequest()
        request.set_DBInstanceId(instance_id)
        request.set_StartTime(start_time)
        request.set_EndTime(end_time)
        
        if db_name:
            request.set_DBName(db_name)
        
        return self._make_request(request)

    def get_sql_samples(self, instance_id, sql_id, start_time, end_time):
        """获取SQL样本数据

        Args:
            instance_id: RDS实例ID
            sql_id: SQL ID
            start_time: 开始时间，Unix时间戳(毫秒)
            end_time: 结束时间，Unix时间戳(毫秒)

        Returns:
            dict: SQL样本数据响应对象
        """
        if not DAS_SDK_AVAILABLE:
            raise Exception("DAS SDK不可用，无法调用GetFullRequestSampleByInstanceId API")

        request = GetFullRequestSampleByInstanceIdRequest()
        request.set_InstanceId(instance_id)
        request.set_SqlId(sql_id)
        request.set_Start(start_time)
        request.set_End(end_time)

        return self._make_request(request)

    def get_table_statistics(self, instance_id, database_name, table_name):
        """获取表统计信息
        
        Args:
            instance_id: RDS实例ID
            database_name: 数据库名称
            table_name: 表名
            
        Returns:
            dict: 表统计信息响应对象
        """
        # 由于阿里云RDS的安全限制，我们无法直接执行SQL查询INFORMATION_SCHEMA
        # 这里返回一个模拟的响应，实际生产环境中需要配置专门的数据库访问权限
        # 或者使用阿里云DAS提供的表结构查询API（如果有的话）
        
        try:
            # 模拟表统计信息，实际应该调用阿里云的表结构API
            # 或者通过配置的数据库连接池直接查询
            self.logger.info(f"尝试获取表统计信息: {instance_id}.{database_name}.{table_name}")
            
            # 返回模拟的表统计信息
            return {
                'Data': {
                    'engine': 'InnoDB',
                    'table_rows': 'Unknown',
                    'data_length': 'Unknown',
                    'index_length': 'Unknown',
                    'avg_row_length': 'Unknown',
                    'auto_increment': 'Unknown',
                    'table_collation': 'utf8mb4_general_ci',
                    'create_time': 'Unknown',
                    'update_time': 'Unknown',
                    'table_comment': f'表 {table_name} 的统计信息（模拟数据）'
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取表统计信息失败: {e}")
            return {
                'Data': {
                    'engine': 'Unknown',
                    'table_rows': 0,
                    'data_length': 0,
                    'index_length': 0,
                    'avg_row_length': 0,
                    'auto_increment': 0,
                    'table_collation': 'Unknown',
                    'create_time': 'Unknown',
                    'update_time': 'Unknown',
                    'table_comment': f'无法获取表 {table_name} 的统计信息'
                }
            }
    
    def execute_sql(self, instance_id, database_name, sql):
        """执行SQL查询
        
        Args:
            instance_id: RDS实例ID
            database_name: 数据库名称
            sql: 要执行的SQL语句
            
        Returns:
            dict: SQL执行结果响应对象
        """
        try:
            # 使用阿里云DAS的SQL执行API
            from aliyunsdkdas.request.v20200116.CreateQueryOptimizeTagRequest import CreateQueryOptimizeTagRequest
            
            # 这里我们需要使用真正的SQL执行API
            # 由于阿里云的安全限制，我们可能需要使用DAS的SQL执行功能
            # 但目前的SDK可能不直接支持任意SQL执行
            
            # 作为临时解决方案，我们可以尝试直接连接数据库
            # 但这需要数据库连接权限和凭据
            
            # 对于SHOW CREATE TABLE，我们可以尝试获取表结构信息
            if sql.strip().upper().startswith('SHOW CREATE TABLE'):
                # 从SQL中提取表名
                import re
                match = re.search(r'SHOW CREATE TABLE `?([^`\s]+)`?', sql, re.IGNORECASE)
                extracted_table_name = match.group(1) if match else 'unknown_table'
                
                # 尝试通过阿里云API获取表结构
                try:
                    # 使用表统计信息API获取一些基本信息
                    table_stats = self.get_table_statistics(instance_id, database_name, extracted_table_name)
                    
                    # 这里我们需要构造一个更真实的DDL
                    # 由于无法直接执行SHOW CREATE TABLE，我们返回一个提示信息
                    ddl_info = f"-- 无法直接获取完整DDL，请在数据库管理工具中执行以下命令：\n-- SHOW CREATE TABLE `{database_name}`.`{extracted_table_name}`;\n\n"
                    ddl_info += f"-- 表基本信息：\n"
                    
                    if table_stats and 'Data' in table_stats:
                        stats = table_stats['Data']
                        if isinstance(stats, dict):
                            ddl_info += f"-- 存储引擎: {stats.get('engine', 'Unknown')}\n"
                            ddl_info += f"-- 表行数: {stats.get('table_rows', 'Unknown')}\n"
                            ddl_info += f"-- 数据大小: {stats.get('data_length', 'Unknown')} bytes\n"
                            ddl_info += f"-- 索引大小: {stats.get('index_length', 'Unknown')} bytes\n"
                            ddl_info += f"-- 字符集: {stats.get('table_collation', 'Unknown')}\n"
                            if stats.get('table_comment'):
                                ddl_info += f"-- 表注释: {stats.get('table_comment')}\n"
                    
                    ddl_info += f"\n-- 请联系DBA获取完整的表结构信息"
                    
                    return {
                        'Data': {
                            'Result': {
                                'Columns': ['Table', 'Create Table'],
                                'Rows': [[
                                    extracted_table_name, 
                                    ddl_info
                                ]]
                            }
                        }
                    }
                except Exception as e:
                    self.logger.error(f"获取表结构信息失败: {e}")
                    return {
                        'Data': {
                            'Result': {
                                'Columns': ['Table', 'Create Table'],
                                'Rows': [[
                                    extracted_table_name, 
                                    f"-- 无法获取表 `{extracted_table_name}` 的结构信息\n-- 错误: {str(e)}\n-- 请联系DBA或在数据库管理工具中查看"
                                ]]
                            }
                        }
                    }
            else:
                # 对于其他SQL，返回提示信息
                return {
                    'Data': {
                        'Result': {
                            'Columns': ['Message'],
                            'Rows': [['此功能暂不支持执行任意SQL查询，请使用专业的数据库管理工具']]
                        }
                    }
                }
        except Exception as e:
            self.logger.error(f"执行SQL失败: {e}")
            return {
                'Data': {
                    'Result': {
                        'Columns': ['Error'],
                        'Rows': [[f'SQL执行失败: {str(e)}']]
                    }
                }
            }


#######################################################
# 业务服务层 - 实例同步服务
#######################################################

class InstanceSyncService:
    """RDS实例同步服务"""
    
    @classmethod
    def sync_all_instances(cls, user=None):
        """同步所有RDS实例
        
        Args:
            user: 执行同步操作的用户
            
        Returns:
            dict: 同步结果统计
        """
        service = AliCloudService()
        
        # 初始化计数器
        result = {
            'total': 0,
            'new': 0,
            'updated': 0,
            'failed': 0,
            'errors': []
        }
        
        try:
            # 获取实例列表
            response = service.list_instances(page_size=100)
            instances = response.get('Items', {}).get('DBInstance', [])
            
            result['total'] = len(instances)
            logger.info(f"发现 {len(instances)} 个RDS实例")
            
            # 同步每个实例
            for instance_data in instances:
                try:
                    with transaction.atomic():
                        instance_id = instance_data.get('DBInstanceId')
                        
                        # 获取详细信息
                        details = service.get_instance_details(instance_id)
                        instance_detail = details.get('Items', {}).get('DBInstanceAttribute', [])[0]
                        
                        # 查找或创建实例记录
                        instance, created = RDSInstance.objects.update_or_create(
                            instance_id=instance_id,
                            defaults={
                                'instance_name': instance_data.get('DBInstanceDescription'),
                                'instance_type': instance_data.get('DBInstanceType'),
                                'engine': instance_data.get('Engine'),
                                'engine_version': instance_data.get('EngineVersion'),
                                'status': instance_data.get('DBInstanceStatus'),
                                'region_id': instance_data.get('RegionId'),
                                'zone_id': instance_data.get('ZoneId'),
                                'connection_string': instance_detail.get('ConnectionString'),
                                'port': instance_detail.get('Port'),
                                'instance_class': instance_data.get('DBInstanceClass'),
                                'create_time': datetime.strptime(
                                    instance_data.get('CreateTime'), "%Y-%m-%dT%H:%M:%SZ"
                                ) if instance_data.get('CreateTime') else timezone.now(),
                                'expire_time': datetime.strptime(
                                    instance_data.get('ExpireTime'), "%Y-%m-%dT%H:%M:%SZ"
                                ) if instance_data.get('ExpireTime') else None,
                                'last_sync_time': timezone.now(),
                            }
                        )
                        
                        # 同步数据库
                        cls._sync_instance_databases(service, instance)
                        
                        # 同步账号
                        cls._sync_instance_accounts(service, instance)
                        
                        # 记录同步统计
                        if created:
                            result['new'] += 1
                        else:
                            result['updated'] += 1
                            
                        # 为执行同步的用户添加访问权限
                        if user and not user.is_superuser:
                            InstanceAccessPermission.objects.get_or_create(
                                user=user,
                                instance=instance,
                                defaults={'permission_type': 'admin'}
                            )
                            
                except Exception as e:
                    result['failed'] += 1
                    error_msg = f"同步实例失败: {instance_id if 'instance_id' in locals() else 'unknown'}, 错误: {str(e)}"
                    result['errors'].append(error_msg)
                    logger.error(error_msg)
            
            return result
        
        except Exception as e:
            error_msg = f"同步所有实例失败: {str(e)}"
            logger.error(error_msg)
            result['errors'].append(error_msg)
            return result
    
    @staticmethod
    def _sync_instance_databases(service, instance):
        """同步实例的数据库信息
        
        Args:
            service: AliCloudService实例
            instance: RDSInstance模型实例
        """
        # 获取数据库列表
        try:
            db_response = service.get_instance_databases(instance.instance_id)
            databases = db_response.get('Databases', {}).get('Database', [])
            
            # 记录当前同步的数据库，用于后续清理不存在的数据库
            current_dbs = []
            
            # 同步每个数据库
            for db_data in databases:
                db_name = db_data.get('DBName')
                if not db_name:
                    continue
                
                # 记录当前处理的数据库
                current_dbs.append(db_name)
                
                # 查找或创建数据库记录
                db, _ = Database.objects.update_or_create(
                    instance=instance,
                    name=db_name,
                    defaults={
                        'description': db_data.get('DBDescription', ''),
                        'status': db_data.get('DBStatus', ''),
                        'engine': db_data.get('Engine', instance.engine),
                        'character_set': db_data.get('CharacterSetName', ''),
                        'last_sync_time': timezone.now()
                    }
                )
            
            # 清理不存在的数据库（可选）
            # Database.objects.filter(instance=instance).exclude(name__in=current_dbs).delete()
            
        except Exception as e:
            logger.error(f"同步数据库失败, 实例ID: {instance.instance_id}, 错误: {str(e)}")
    
    @classmethod
    def _sync_instance_accounts(cls, service, instance):
        """同步实例的账号列表
        
        Args:
            service: AliCloudService实例
            instance: RDSInstance模型实例
        """
        try:
            response = service.get_instance_accounts(instance.instance_id)
            accounts = response.get('Accounts', {}).get('DBInstanceAccount', [])
            
            # 记录当前同步的账号名称
            current_accounts = []
            
            for account_data in accounts:
                account_name = account_data.get('AccountName')
                current_accounts.append(account_name)
                
                DBAccount.objects.update_or_create(
                    instance=instance,
                    account_name=account_name,
                    defaults={
                        'account_type': account_data.get('AccountType'),
                        'account_status': account_data.get('AccountStatus'),
                        'description': account_data.get('AccountDescription', ''),
                        'privileges': account_data.get('DatabasePrivileges', {}).get('DatabasePrivilege', []),
                        'last_sync_time': timezone.now(),
                    }
                )
            
            # 将不存在的账号标记为已删除
            DBAccount.objects.filter(instance=instance).exclude(account_name__in=current_accounts).update(
                account_status='Deleted',
                last_sync_time=timezone.now()
            )
            
        except Exception as e:
            logger.error(f"同步实例 {instance.instance_id} 的账号失败: {str(e)}")
            raise


#######################################################
# 业务服务层 - SQL模板同步管理器
#######################################################

class SQLTemplateSyncManager:
    """SQL模板同步管理器"""
    
    @classmethod
    def sync_templates(cls, date=None, instance_ids=None):
        """同步SQL模板数据
        
        Args:
            date: 日期字符串，格式为YYYY-MM-DD，或者date对象
            instance_ids: 实例ID列表，如果为空则同步所有实例
            
        Returns:
            dict: 同步结果统计
        """
        service = AliCloudService()

        logger.debug(f"api参数date:{date},,,ins:{instance_ids}")
        
        # 初始化统计结果
        result = {
            'total_instances': 0,
            'successful_instances': 0,
            'failed_instances': 0,
            'total_templates': 0,
            'errors': [],
            'details': []
        }
        
        # 修复日期处理逻辑，确保date是date对象
        if not date:
            # 如果未指定日期，则使用昨天的日期（date对象）
            date = (timezone.now() - timedelta(days=1)).date()
        elif isinstance(date, str):
            # 如果传入的是字符串，转换为date对象
            try:
                date = datetime.strptime(date, '%Y-%m-%d').date()
            except ValueError as e:
                logger.error(f"日期格式错误: {date}, 错误: {str(e)}")
                result['errors'].append(f"日期格式错误: {date}")
                return result
        elif isinstance(date, datetime):
            # 如果传入的是datetime对象，转换为date对象
            date = date.date()
        # 如果已经是date对象，直接使用
        
        logger.info(f"使用日期对象: {date} (类型: {type(date)})")
        
        # 确定要同步的实例列表
        if instance_ids:
            instances = RDSInstance.objects.filter(instance_id__in=instance_ids, status='Running')
            # instances = RDSInstance.objects.filter(instance_id__in=["rm-bp11k4085qib7xuoz"], status="Running")
        else:
            instances = RDSInstance.objects.filter(status='Running')
        
        result['total_instances'] = instances.count()
        logger.info(f"准备同步 {result['total_instances']} 个实例的SQL模板数据")
        
        # 逐个实例进行处理
        for instance in instances:
            instance_stat = {
                'instance_id': instance.instance_id,
                'instance_name': instance.instance_name,
                'status': 'processing',
                'templates_count': 0,
                'error': None
            }
            
            try:
                logger.info(f"开始同步实例 {instance.instance_id} 的SQL模板")
                
                # 分页获取SQL模板
                page_no = 1
                page_size = 100
                all_templates = []
                
                # 尝试获取实例的SQL模板
                try:
                    while True:
                        # 获取SQL模板，每次获取100条
                        # 注意：这里需要传字符串给阿里云API
                        date_str = date.strftime('%Y-%m-%d')
                        templates_response = service.get_sql_templates(
                            instance_id=instance.instance_id, 
                            date=date_str,  # 传字符串给API
                            page_no=page_no,
                            page_size=page_size,
                            order_by="count"
                        )
                        
                        # 提取模板数据
                        templates = []
                        if templates_response and 'Data' in templates_response:
                            result_data = templates_response['Data'].get('Result', {})
                            if result_data:
                                templates = result_data.get('List', [])
                        
                        # 合并模板数据
                        all_templates.extend(templates)
                        
                        logger.info(f"实例 {instance.instance_id} 第{page_no}页获取到 {len(templates)} 条SQL模板")
                        
                        # 检查是否有更多数据需要获取
                        if len(templates) < page_size or not templates:
                            break
                        
                        # 下一页
                        page_no += 1
                    
                    logger.info(f"实例 {instance.instance_id} 共获取到 {len(all_templates)} 条SQL模板")
                except Exception as e:
                    logger.error(f"获取实例 {instance.instance_id} 的SQL模板数据失败: {str(e)}")
                    raise
                
                # 使用单独的事务保存每个实例的数据
                with transaction.atomic():
                    # 删除相同日期的旧数据（避免重复）
                    # delete_count = SQLTemplate.objects.filter(
                    #     instance_id=instance.instance_id,
                    #     timezone=date
                    # ).delete()[0]
                    # logger.info(f"删除实例 {instance.instance_id} 日期为 {date} 的旧数据 {delete_count} 条")
                    
                    # 批量创建SQL模板记录
                    template_objs = []
                    for template in all_templates:
                        # API响应中的SQL ID字段名可能是SqlId
                        sql_id = template.get('SqlId')
                        if not sql_id:
                            continue
                        
                        # 创建SQL模板实例，进行字段名称映射
                        # 关键修复：确保timezone字段传入的是date对象
                        obj, created = SQLTemplate.objects.update_or_create(
                            sql_id=sql_id,
                            instance_id=instance.instance_id,
                            timezone=date,  # 现在这里是date对象，不是字符串
                            defaults={
                                'database_name': template.get('Database', ''),
                                'sql_template': template.get('Psql', ''),
                                'sql_count': template.get('Count', 0),
                                'avg_query_time': template.get('AvgRt', 0),
                                'avg_lock_time': round(template.get('LockWaitTime', 0)/template.get('Count') * 1000000, 3) if template.get('Count', 0) > 0 else 0,  # 转换为微秒，避免除零错误
                                'avg_rows_examined': template.get('AvgExaminedRows', 0),
                                'avg_rows_sent': template.get('AvgReturnedRows', 0),
                                'avg_rows_affected': template.get('AvgUpdatedRows', 0),
                                
                                # 其他字段
                                'error_count': template.get('ErrorCount', 0),
                                'logical_read': template.get('LogicalRead', 0),
                                'rt_greater_than_one_second_count': template.get('RtGreaterThanOneSecondCount', 0),
                                'table_names': template.get('Tables', ''),
                                'port': template.get('Port', 0),
                                'vpc_id': template.get('VpcId', '')
                            }
                        )
                        instance_stat['templates_count'] += 1
                        logger.debug(f"成功保存SQL模板: {sql_id}, {'新建' if created else '更新'}")
                
                # 更新统计信息
                instance_stat['status'] = 'success'
                result['successful_instances'] += 1
                logger.info(f"成功同步实例 {instance.instance_id} 的SQL模板，共 {instance_stat['templates_count']} 条")
                
            except Exception as e:
                error_msg = f"同步SQL模板失败, 实例ID: {instance.instance_id}, 错误: {str(e)}"
                logger.error(error_msg, exc_info=True)
                instance_stat['status'] = 'failed'
                instance_stat['error'] = str(e)
                result['failed_instances'] += 1
                result['errors'].append(error_msg)
            
            result['details'].append(instance_stat)
            
            # 添加短暂延迟，避免API请求过于频繁
            time.sleep(0.1)
        
        logger.info(f"SQL模板同步完成: 总实例数 {result['total_instances']}, 成功 {result['successful_instances']}, 失败 {result['failed_instances']}, 总模板数 {result['total_templates']}")
        return result


#######################################################
# 业务服务层 - 慢查询同步管理器
#######################################################

class SlowQuerySyncManager:
    """慢查询同步管理器 - 负责同步参数处理和结果统计"""
    
    @classmethod
    def sync_slow_queries(cls, instance_id=None, start_time=None, end_time=None, user=None):
        """同步慢查询数据
        
        Args:
            instance_id: 指定要同步的实例ID，如果为None则同步所有实例
            start_time: 开始时间，格式为"YYYY-MM-DD HH:MM:SS"
            end_time: 结束时间，格式为"YYYY-MM-DD HH:MM:SS"
            user: 执行同步的用户
            
        Returns:
            dict: 同步结果统计
        """
        service = AliCloudService()
        
        # 设置默认时间范围：如果未指定，使用过去24小时
        if not start_time:
            start_time = (timezone.now() - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        if not end_time:
            end_time = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 获取要同步的实例
        if instance_id:
            instances = RDSInstance.objects.filter(instance_id=instance_id, status='Running')
        else:
            # 如果用户不是超级用户，只同步有权限的实例
            if user and not user.is_superuser:
                instances = RDSInstance.objects.filter(
                    instanceaccesspermission__user=user,
                    status='Running'
                )
            else:
                instances = RDSInstance.objects.filter(status='Running')
        
        # 初始化统计结果
        result = {
            'total_instances': instances.count(),
            'successful_instances': 0,
            'failed_instances': 0,
            'total_slow_queries': 0,
            'errors': [],
            'details': []
        }
        
        # 处理每个实例
        for instance in instances:
            instance_stat = {
                'instance_id': instance.instance_id,
                'instance_name': instance.instance_name,
                'status': 'processing',
                'slow_queries_count': 0,
                'error': None
            }
            
            try:
                # 获取慢查询日志
                slow_log_response = service.get_slow_query_logs(
                    instance.instance_id, start_time, end_time
                )
                
                # 提取慢查询日志项
                slow_logs = []
                if slow_log_response and 'Items' in slow_log_response:
                    slow_logs = slow_log_response['Items'].get('SQLSlowLog', [])
                
                # 如果有慢查询记录，则获取详细信息
                slow_queries = []
                for slow_log in slow_logs:
                    # 获取慢查询详细记录
                    db_name = slow_log.get('DBName')
                    records_response = service.get_slow_query_records(
                        instance.instance_id, start_time, end_time, db_name
                    )
                    
                    # 提取记录
                    if records_response and 'Items' in records_response:
                        records = records_response['Items'].get('SQLSlowRecord', [])
                        slow_queries.extend(records)
                
                # 开始数据库事务
                with transaction.atomic():
                    # 删除相同时间范围的旧数据
                    SlowQueryLog.objects.filter(
                        instance_id=instance.instance_id,
                        execute_time__gte=start_time,
                        execute_time__lte=end_time
                    ).delete()
                    
                    # 批量创建慢查询记录
                    query_objs = []
                    for query_data in slow_queries:
                        query_id = query_data.get('SQLHash')
                        execute_time = query_data.get('ExecutionStartTime')
                        
                        if not query_id or not execute_time:
                            continue
                        
                        # 提取用户名和主机
                        user_host = query_data.get('HostAddress', '')
                        
                        # 创建慢查询对象
                        query = SlowQueryLog(
                            instance_id=instance.instance_id,
                            instance_name=instance.instance_name,
                            query_id=query_id,
                            execute_time=execute_time,
                            db_name=query_data.get('DBName', ''),
                            sql_text=query_data.get('SQLText', ''),
                            query_time=query_data.get('QueryTimes', 0),
                            lock_time=query_data.get('LockTimes', 0),
                            rows_examined=query_data.get('ParseRowCounts', 0),
                            rows_sent=query_data.get('ReturnRowCounts', 0),
                            user_host=user_host,
                            thread_id=query_data.get('ThreadID', ''),
                            client_ip=query_data.get('ClientIP', '')
                        )
                        
                        # 设置创建者信息，使用ID和用户名而不是外键
                        if user:
                            query.created_by_id = user.id
                            query.created_by_username = user.username
                        
                        query_objs.append(query)
                    
                    # 批量保存
                    if query_objs:
                        SlowQueryLog.objects.bulk_create(query_objs)
                    
                    instance_stat['slow_queries_count'] = len(query_objs)
                    result['total_slow_queries'] += len(query_objs)
                
                # 更新统计信息
                instance_stat['status'] = 'success'
                result['successful_instances'] += 1
                
            except Exception as e:
                error_msg = f"同步慢查询失败, 实例ID: {instance.instance_id}, 错误: {str(e)}"
                logger.error(error_msg)
                instance_stat['status'] = 'failed'
                instance_stat['error'] = str(e)
                result['failed_instances'] += 1
                result['errors'].append(error_msg)
            
            result['details'].append(instance_stat)
        
        return result 


#######################################################
# 业务服务层 - SQL审计评分服务
#######################################################

class SQLAuditScoreService:
    """SQL审计评分服务"""
    
    @classmethod
    def calculate_score(cls, instance_id, date):
        """计算SQL审计评分
        
        Args:
            instance_id: RDS实例ID
            date: 日期，格式为YYYY-MM-DD
            
        Returns:
            dict: SQL审计评分结果，包含评分和分析计数
        """
        from loguru import logger
        from django.db.models import Avg
        import datetime
        
        # 如果传入的是字符串日期，转换为日期对象
        if isinstance(date, str):
            date = datetime.datetime.strptime(date, '%Y-%m-%d').date()
        
        # 获取指定实例和日期的所有SQL模板
        sql_templates = SQLTemplate.objects.filter(
            instance_id=instance_id,
            timezone=date
        )
        
        if not sql_templates.exists():
            logger.warning(f"未找到实例{instance_id}在{date}的SQL模板数据")
            return {
                'success': False,
                'message': f'未找到实例{instance_id}在{date}的SQL模板数据',
                'count': 0,
                'avg_score': 0
            }
        
        # 获取实例的CPU核数信息
        try:
            instance = RDSInstance.objects.get(instance_id=instance_id)
            cpu_cores = instance.cpu_cores
            logger.info(f"获取到实例{instance_id}的CPU核数: {cpu_cores}")
        except Exception as e:
            logger.warning(f"获取实例{instance_id}的CPU核数失败: {str(e)}，使用默认值")
            cpu_cores = None
        
        # 对每个SQL模板进行评分
        count = 0
        for sql_template in sql_templates:
            try:
                cls.analyze_sql(sql_template, cpu_cores)
                count += 1
            except Exception as e:
                logger.error(f"SQL模板{sql_template.sql_id}评分失败: {str(e)}")
                continue
        
        # 计算平均风险评分
        avg_score = SQLAuditScore.objects.filter(
            instance_id=instance_id,
            timezone=date
        ).aggregate(avg_score=Avg('risk_score')).get('avg_score', 0)
        
        if avg_score is None:
            avg_score = 0
        
        return {
            'success': True,
            'message': f'成功评分{count}个SQL模板',
            'count': count,
            'avg_score': round(avg_score, 2)
        }
    
    @classmethod
    def analyze_sql(cls, sql_template, cpu_cores=None):
        """分析SQL模板并进行评分
        
        Args:
            sql_template: SQLTemplate对象
            cpu_cores: RDS实例的CPU核数，用于评分调整
            
        Returns:
            SQLAuditScore: 分析结果对象
        """
        from loguru import logger
        
        # 初始化分析结果
        analysis_result = []
        optimization_suggestion = []
        
        # 提取SQL信息
        sql_text = sql_template.sql_template.lower() if sql_template.sql_template else ""
        
        # 检查是否包含EXPLAIN信息，如果有，则提取执行计划信息
        explain_match = re.search(r'explain\s+(.*)', sql_text)
        if explain_match:
            sql_text = explain_match.group(1)
            
        # 获取SQL模板类型的评分配置
        try:
            # 首先尝试获取名为 audit_score 的配置
            config = SlowQueryScoreConfig.objects.filter(name="audit_score", config_type='sqltemplate').first()
            if not config:
                # 如果不存在，则获取SQL模板类型的激活配置
                config = SlowQueryScoreConfig.get_active_config(config_type='sqltemplate')

            if config:
                logger.info(f"使用SQL模板评分配置: {config.name}")
            else:
                logger.warning("未找到SQL模板评分配置，使用默认值")
                config = None
        except Exception as e:
            logger.error(f"获取SQL模板评分配置失败，使用默认值: {str(e)}")
            # 使用默认配置
            config = None
        
        # 风险评分标准参数
        # 1. 查询qps,周期24h
        query_count = round(sql_template.sql_count/24/60/60, 2)
        query_count_min = config.query_count_min if config else 5
        query_count_max = config.query_count_max if config else 500
        query_count_weight = config.query_count_weight if config else 0.3
        query_count_curve = config.query_count_curve if config else 'linear'
        
        # 2. 单次查询时间(毫秒)
        query_time = sql_template.avg_query_time
        query_time_min = config.query_time_min if config else 0
        query_time_max = config.query_time_max if config else 10000  # 10秒
        query_time_weight = config.query_time_weight if config else 0.2
        query_time_curve = config.query_time_curve if config else 'sine'
        
        # 3. 总扫描行数
        avg_scan_rows = sql_template.avg_rows_examined
        scan_rows = avg_scan_rows * sql_template.sql_count
        scan_rows_min = config.scan_rows_min if config else 1
        scan_rows_max = config.scan_rows_max if config else 1000000  # 100万行
        scan_rows_weight = config.scan_rows_weight if config else 0.4
        scan_rows_curve = config.scan_rows_curve if config else 'polynomial'
        
        # 4. 单次影响(返回)行数
        affected_rows = int(sql_template.avg_rows_affected)
        affected_rows_min = config.affected_rows_min if config else 0
        affected_rows_max = config.affected_rows_max if config else 500
        affected_rows_weight = config.affected_rows_weight if config else 0.05
        affected_rows_curve = config.affected_rows_curve if config else 'linear'
        
        # 5. 锁等待时间
        lock_time = sql_template.avg_lock_time
        lock_time_min = config.lock_time_min if config else 0
        lock_time_max = config.lock_time_max if config else 1000
        lock_time_weight = config.lock_time_weight if config else 0.05
        lock_time_curve = config.lock_time_curve if config else 'exponential'
        
        # 计算各项得分比例(0-1之间)
        # 根据曲线类型计算得分
        def calculate_score(value, min_val, max_val, curve_type, config=None):
            # 超出范围处理
            if value <= min_val:
                return 0
            if value >= max_val:
                return 1
                
            # 标准化到0-1区间
            normalized = (value - min_val) / (max_val - min_val)
            
            # 根据曲线类型计算
            if curve_type == 'linear':
                # 线性曲线: Y = X
                return normalized
            elif curve_type == 'exponential':
                # 指数曲线: Y = 2^(X * log₂(100)) / 100
                return pow(2, normalized * math.log2(100)) / 100
            elif curve_type == 'logarithmic':
                # 对数曲线: Y = log₁₀(9*X+1)
                return math.log10(9 * normalized + 1)
            elif curve_type == 'sine':
                # 正弦曲线: Y = sin(X * π/2)
                return math.sin(normalized * math.pi / 2)
            elif curve_type == 'polynomial':
                # 多项式曲线: Y = a + b·X + c·X² + d·X³ + e·X⁴ + f·X⁵
                # 获取系数
                if config:
                    a, b, c, d, e, f = config.polynomial_a, config.polynomial_b, config.polynomial_c, config.polynomial_d, config.polynomial_e, config.polynomial_f
                else:
                    a, b, c, d, e, f = 0.0, 0.02, 0.001, 0.0005, 0.00001, 0.000001
                
                # 这里将范围缩小到0-20，适合多项式计算
                x = normalized * 20
                return min(1.0, a + b*x + c*x**2 + d*x**3 + e*x**4 + f*x**5)
            else:
                # 默认使用线性曲线
                return normalized
        
        # 计算各项得分
        query_count_score_raw = calculate_score(query_count/(cpu_cores if cpu_cores else 1), query_count_min, query_count_max, query_count_curve, config)
        query_time_score_raw = calculate_score(query_time, query_time_min, query_time_max, query_time_curve, config)
        scan_rows_score_raw = calculate_score(scan_rows, scan_rows_min, scan_rows_max, scan_rows_curve, config) * min(sql_template.logical_read / sql_template.sql_count / 100, 1)
        avg_scan_rows_score_raw = calculate_score(avg_scan_rows, 1, 10000, 'exponential', config)
        affected_rows_score_raw = calculate_score(affected_rows, affected_rows_min, affected_rows_max, affected_rows_curve, config)
        lock_time_score_raw = calculate_score(lock_time, lock_time_min, lock_time_max, lock_time_curve, config)
        
        # 转换为0-100的整数分数
        query_count_score = int(query_count_score_raw * 100)
        query_time_score = int(query_time_score_raw * 100)
        scan_rows_score = int(max(scan_rows_score_raw * 100, avg_scan_rows_score_raw * 100))
        affected_rows_score = int(affected_rows_score_raw * 100)
        lock_time_score = int(lock_time_score_raw * 100)
        
        # 记录各项得分
        logger.debug(f"SQL审计评分明细: SQL_ID={sql_template.sql_id}, 查询次数={query_count_score}, 查询时间={query_time_score}, " +
                    f"扫描行数={scan_rows_score}, 影响行数={affected_rows_score}, 锁等待时间={lock_time_score}")
        
        # 计算加权总分
        total_score = (query_count_score_raw * query_count_weight + 
                    query_time_score_raw * query_time_weight + 
                    scan_rows_score / 100 * scan_rows_weight + 
                    affected_rows_score_raw * affected_rows_weight + 
                    lock_time_score_raw * lock_time_weight)
        
        # 总分(0-1)换算为风险级别(0-100)
        risk_score = int(total_score * 100)

        if sql_template.sql_count < 500 and risk_score > 40:
            risk_score = int(risk_score * 0.7)
        
        # 风险特征标识
        table_scans = False
        no_indexes = False
        poor_indexes = False
        temporary_tables = False
        filesort = False
        
        # 分析结果描述及优化建议
        if query_count_score > 70:
            analysis_result.append(f"查询频率较高,QPS为（{query_count}，CPU核数为{cpu_cores}）")
            if query_count_score > 100:
                optimization_suggestion.append("考虑使用缓存或优化应用逻辑减少查询次数")
        
        if query_time > 1000:  # 超过1秒
            analysis_result.append(f"查询耗时较长（{query_time/1000:.2f}秒）")
            optimization_suggestion.append("优化查询语句或添加适当的索引")
        
        if scan_rows_score > 70:
            analysis_result.append(f"总扫描行数较大（{scan_rows}行）")
            if avg_scan_rows > 10000:
                optimization_suggestion.append("检查WHERE条件，考虑增加或优化索引")
        
        if lock_time > 1000:  # 超过1000微秒
            analysis_result.append(f"锁等待时间较长（{lock_time}微秒）")
            optimization_suggestion.append("检查事务设计，避免长时间持有锁")
        
        # 检查SQL文本中的风险模式
        # 1. 检查是否有全表扫描
        if re.search(r'select\s+.*\s+from\s+\w+\s+(where|order|group|having|limit)', sql_text) and not re.search(r'where', sql_text):
            table_scans = True
            analysis_result.append("执行全表扫描")
            optimization_suggestion.append("添加WHERE条件限制查询范围")
        
        # 2. 检查JOIN操作
        if re.search(r'join', sql_text):
            if not re.search(r'inner join|left join|right join', sql_text):
                analysis_result.append("使用了隐式JOIN")
                optimization_suggestion.append("使用显式JOIN语法提高可读性")
            
            # 检查多表JOIN
            join_count = len(re.findall(r'join', sql_text))
            if join_count > 3:
                analysis_result.append(f"多表JOIN操作（{join_count}个表）")
                optimization_suggestion.append("考虑拆分查询或使用临时表")
        
        # 3. 检查是否使用了函数在索引字段上
        if re.search(r'(where|on)\s+\w+\s*\(\s*\w+\s*\)', sql_text):
            poor_indexes = True
            analysis_result.append("在WHERE/ON子句中对字段使用了函数")
            optimization_suggestion.append("避免在索引字段上使用函数，会导致索引失效")
        
        # 4. 检查是否使用了NOT IN, NOT EXISTS
        if re.search(r'not\s+in|not\s+exists', sql_text):
            analysis_result.append("使用了NOT IN或NOT EXISTS")
            optimization_suggestion.append("考虑使用LEFT JOIN ... IS NULL替代")
        
        # 5. 检查是否使用了LIKE '%...%'
        if re.search(r'like\s+[\'"]%.*%[\'"]', sql_text):
            poor_indexes = True
            analysis_result.append("使用了两边都有%的LIKE模糊查询")
            optimization_suggestion.append("避免使用'%'开头的LIKE，会导致索引失效")
        
        # 6. 检查ORDER BY和GROUP BY
        if re.search(r'order\s+by\s+\w+', sql_text) and scan_rows > 10000:
            filesort = True
            analysis_result.append("大数据量排序操作")
            optimization_suggestion.append("考虑为ORDER BY字段创建索引")
        
        if re.search(r'group\s+by', sql_text) and scan_rows > 10000:
            temporary_tables = True
            analysis_result.append("大数据量分组操作")
            optimization_suggestion.append("考虑为GROUP BY字段创建索引")
        
        # 7. 检查临时表创建
        if re.search(r'create\s+temporary\s+table', sql_text) or temporary_tables:
            temporary_tables = True
            analysis_result.append("创建临时表")
            optimization_suggestion.append("考虑优化查询逻辑，减少临时表的使用")
        
        # 将分析结果和优化建议转换为字符串
        analysis_result_str = '\n'.join(analysis_result) if analysis_result else "无明显问题"
        optimization_suggestion_str = '\n'.join(optimization_suggestion) if optimization_suggestion else "无优化建议"
        
        # 创建或更新SQL审计评分记录
        try:
            audit_score, created = SQLAuditScore.objects.update_or_create(
                sql_id=sql_template.sql_id,
                instance_id=sql_template.instance_id,
                timezone=sql_template.timezone,
                defaults={
                    'table_scans': table_scans,
                    'no_indexes': no_indexes,
                    'poor_indexes': poor_indexes,
                    'temporary_tables': temporary_tables,
                    'filesort': filesort,
                    'analysis_result': analysis_result_str,
                    'optimization_suggestion': optimization_suggestion_str,
                    'risk_score': risk_score,
                    'query_count_score': query_count_score,
                    'query_time_score': query_time_score,
                    'scan_rows_score': scan_rows_score,
                    'affected_rows_score': affected_rows_score,
                    'lock_time_score': lock_time_score,
                }
            )
            
            if created:
                logger.info(f"创建SQL审计评分: SQL_ID={sql_template.sql_id}, 风险评分={risk_score}")
            else:
                logger.info(f"更新SQL审计评分: SQL_ID={sql_template.sql_id}, 风险评分={risk_score}")
                
            return audit_score
        except Exception as e:
            logger.error(f"保存SQL审计评分失败: {str(e)}")
            raise
    
    @classmethod
    def get_score_history(cls, instance_id):
        """获取SQL审计评分历史
        
        Args:
            instance_id: RDS实例ID
            
        Returns:
            list: SQL审计评分历史记录
        """
        from django.db.models import Avg
        
        # 获取指定实例的SQL审计评分历史统计
        result = SQLAuditScore.objects.filter(instance_id=instance_id).values('timezone')\
            .annotate(avg_score=Avg('risk_score'))\
            .order_by('-timezone')
            
        # 格式化返回结果
        history = []
        for item in result:
            history.append({
                'date': item['timezone'].strftime('%Y-%m-%d'),
                'score': round(item['avg_score'], 2) if item['avg_score'] else 0
            })
            
        return history
    
    @classmethod
    def get_score_details(cls, instance_id, date):
        """获取SQL审计评分详情
        
        Args:
            instance_id: RDS实例ID
            date: 日期，格式为YYYY-MM-DD
            
        Returns:
            dict: SQL审计评分详情
        """
        import datetime
        from django.db.models import Avg, Count, Sum, Q
        
        # 如果传入的是字符串日期，转换为日期对象
        if isinstance(date, str):
            date = datetime.datetime.strptime(date, '%Y-%m-%d').date()
        
        # 获取评分统计信息
        stats = SQLAuditScore.objects.filter(instance_id=instance_id, timezone=date).aggregate(
            avg_score=Avg('risk_score'),
            count=Count('id'),
            high_risk_count=Count('id', filter=Q(risk_score__gte=70)),
            medium_risk_count=Count('id', filter=Q(risk_score__gte=40, risk_score__lt=70)),
            low_risk_count=Count('id', filter=Q(risk_score__lt=40)),
            table_scans_count=Count('id', filter=Q(table_scans=True)),
            no_indexes_count=Count('id', filter=Q(no_indexes=True)),
            poor_indexes_count=Count('id', filter=Q(poor_indexes=True)),
            temporary_tables_count=Count('id', filter=Q(temporary_tables=True)),
            filesort_count=Count('id', filter=Q(filesort=True))
        )
        
        # 获取评分详情列表
        scores = SQLAuditScore.objects.filter(instance_id=instance_id, timezone=date)\
            .order_by('-risk_score')[:100]  # 限制返回前100条
            
        # 格式化返回结果
        details = []
        for score in scores:
            sql_template = score.sql_template
            sql_text = sql_template.sql_template if sql_template else ""
            
            # 如果SQL文本过长，截断显示
            if len(sql_text) > 200:
                sql_text = sql_text[:200] + "..."
                
            details.append({
                'sql_id': score.sql_id,
                'database_name': sql_template.database_name if sql_template else "",
                'sql_text': sql_text,
                'risk_score': score.risk_score,
                'query_count': sql_template.sql_count if sql_template else 0,
                'avg_query_time': sql_template.avg_query_time if sql_template else 0,
                'avg_rows_examined': sql_template.avg_rows_examined if sql_template else 0,
                'analysis_result': score.analysis_result,
                'optimization_suggestion': score.optimization_suggestion,
                'table_scans': score.table_scans,
                'no_indexes': score.no_indexes,
                'poor_indexes': score.poor_indexes,
                'temporary_tables': score.temporary_tables,
                'filesort': score.filesort
            })
        
        return {
            'date': date.strftime('%Y-%m-%d'),
            'stats': {
                'avg_score': round(stats['avg_score'], 2) if stats['avg_score'] else 0,
                'count': stats['count'],
                'high_risk_count': stats['high_risk_count'],
                'medium_risk_count': stats['medium_risk_count'],
                'low_risk_count': stats['low_risk_count'],
                'risk_features': {
                    'table_scans': stats['table_scans_count'],
                    'no_indexes': stats['no_indexes_count'],
                    'poor_indexes': stats['poor_indexes_count'],
                    'temporary_tables': stats['temporary_tables_count'],
                    'filesort': stats['filesort_count']
                }
            },
            'details': details
        }
    
    @classmethod
    def check_scan_rows_changes(cls, instance_id, current_date, days_back=7):
        """检查扫描行数变化趋势
        
        Args:
            instance_id: RDS实例ID
            current_date: 当前日期
            days_back: 回溯天数，默认7天
            
        Returns:
            dict: 扫描行数变化分析结果
        """
        import datetime
        from django.db.models import Avg, Max, Min, Count
        from loguru import logger
        
        # 如果传入的是字符串日期，转换为日期对象
        if isinstance(current_date, str):
            current_date = datetime.datetime.strptime(current_date, '%Y-%m-%d').date()
            
        # 计算历史日期范围
        start_date = current_date - datetime.timedelta(days=days_back)
        
                 # 获取历史数据
        from django.db import models
        historical_data = SQLTemplate.objects.filter(
            instance_id=instance_id,
            timezone__gte=start_date,
            timezone__lte=current_date
        ).values('sql_id', 'timezone').annotate(
            total_scan_rows=models.F('avg_rows_examined') * models.F('sql_count'),
            avg_scan_rows=Avg('avg_rows_examined'),
            sql_count=models.F('sql_count')
        ).order_by('sql_id', 'timezone')
        
        # 按SQL_ID分组分析变化趋势
        scan_changes = {}
        for data in historical_data:
            sql_id = data['sql_id']
            if sql_id not in scan_changes:
                scan_changes[sql_id] = []
            scan_changes[sql_id].append({
                'date': data['timezone'],
                'total_scan_rows': data['total_scan_rows'],
                'avg_scan_rows': data['avg_scan_rows'],
                'sql_count': data['sql_count']
            })
        
        # 分析每个SQL的扫描行数变化
        analysis_results = []
        for sql_id, history in scan_changes.items():
            if len(history) < 2:
                continue  # 至少需要2个数据点才能比较
                
            # 按日期排序
            history.sort(key=lambda x: x['date'])
            
            # 计算变化趋势
            latest = history[-1]
            previous = history[-2] if len(history) >= 2 else history[0]
            
            # 计算总扫描行数变化率
            total_change_rate = 0
            if previous['total_scan_rows'] > 0:
                total_change_rate = (latest['total_scan_rows'] - previous['total_scan_rows']) / previous['total_scan_rows'] * 100
            
            # 计算平均扫描行数变化率
            avg_change_rate = 0
            if previous['avg_scan_rows'] > 0:
                avg_change_rate = (latest['avg_scan_rows'] - previous['avg_scan_rows']) / previous['avg_scan_rows'] * 100
            
            # 计算执行次数变化率
            count_change_rate = 0
            if previous['sql_count'] > 0:
                count_change_rate = (latest['sql_count'] - previous['sql_count']) / previous['sql_count'] * 100
            
            # 风险等级判断
            risk_level = 'low'
            risk_reasons = []
            
            if abs(total_change_rate) > 200:  # 总扫描行数变化超过200%
                risk_level = 'high'
                risk_reasons.append(f'总扫描行数变化过大: {total_change_rate:.1f}%')
            elif abs(total_change_rate) > 100:
                risk_level = 'medium'
                risk_reasons.append(f'总扫描行数变化较大: {total_change_rate:.1f}%')
                
            if abs(avg_change_rate) > 300:  # 平均扫描行数变化超过300%
                risk_level = 'high'
                risk_reasons.append(f'平均扫描行数变化过大: {avg_change_rate:.1f}%')
            elif abs(avg_change_rate) > 150:
                if risk_level == 'low':
                    risk_level = 'medium'
                risk_reasons.append(f'平均扫描行数变化较大: {avg_change_rate:.1f}%')
            
            # 异常模式检测
            if latest['total_scan_rows'] > 1000000 and total_change_rate > 50:  # 大量扫描且增长
                risk_level = 'high'
                risk_reasons.append('大量扫描行数且持续增长')
                
            if count_change_rate > 100 and avg_change_rate > 50:  # 执行次数和扫描行数都增长
                if risk_level == 'low':
                    risk_level = 'medium'
                risk_reasons.append('执行频率和扫描复杂度同时增长')
            
            analysis_results.append({
                'sql_id': sql_id,
                'current_total_scan_rows': latest['total_scan_rows'],
                'current_avg_scan_rows': latest['avg_scan_rows'],
                'current_sql_count': latest['sql_count'],
                'total_change_rate': round(total_change_rate, 2),
                'avg_change_rate': round(avg_change_rate, 2),
                'count_change_rate': round(count_change_rate, 2),
                'risk_level': risk_level,
                'risk_reasons': risk_reasons,
                'history_days': len(history)
            })
        
        # 按风险等级和变化率排序
        analysis_results.sort(key=lambda x: (
            0 if x['risk_level'] == 'high' else 1 if x['risk_level'] == 'medium' else 2,
            -abs(x['total_change_rate'])
        ))
        
        logger.info(f"完成实例{instance_id}的扫描行数变化检查，发现{len(analysis_results)}个SQL变化")
        
        return {
            'instance_id': instance_id,
            'analysis_date': current_date.strftime('%Y-%m-%d'),
            'days_analyzed': days_back,
            'total_sql_count': len(analysis_results),
            'high_risk_count': len([r for r in analysis_results if r['risk_level'] == 'high']),
            'medium_risk_count': len([r for r in analysis_results if r['risk_level'] == 'medium']),
            'low_risk_count': len([r for r in analysis_results if r['risk_level'] == 'low']),
            'changes': analysis_results[:50]  # 返回前50个最重要的变化
        }
    
    @classmethod
    def integrate_scan_changes_to_score(cls, sql_template, scan_change_data=None, cpu_cores=None):
        """将扫描行数变化分析集成到评分系统中
        
        Args:
            sql_template: SQLTemplate对象
            scan_change_data: 扫描行数变化数据（可选）
            cpu_cores: CPU核数
            
        Returns:
            dict: 包含扫描行数变化的评分调整信息
        """
        from loguru import logger
        
        # 如果没有提供变化数据，尝试获取
        if scan_change_data is None:
            changes_result = cls.check_scan_rows_changes(
                sql_template.instance_id, 
                sql_template.timezone, 
                days_back=3  # 检查最近3天
            )
            
            # 查找当前SQL的变化数据
            scan_change_data = None
            for change in changes_result.get('changes', []):
                if change['sql_id'] == sql_template.sql_id:
                    scan_change_data = change
                    break
        
        # 默认不调整分数
        score_adjustment = 0
        change_risk_level = 'low'
        change_analysis = []
        
        if scan_change_data:
            change_risk_level = scan_change_data['risk_level']
            total_change_rate = scan_change_data['total_change_rate']
            avg_change_rate = scan_change_data['avg_change_rate']
            
            # 根据变化情况调整评分
            if change_risk_level == 'high':
                score_adjustment = 15  # 高风险增加15分
                change_analysis.append("扫描行数变化异常，风险较高")
            elif change_risk_level == 'medium':
                score_adjustment = 8   # 中等风险增加8分
                change_analysis.append("扫描行数变化较大，需要关注")
            
            # 添加具体的变化信息到分析结果
            if abs(total_change_rate) > 50:
                change_analysis.append(f"总扫描行数变化{total_change_rate:+.1f}%")
            if abs(avg_change_rate) > 50:
                change_analysis.append(f"平均扫描行数变化{avg_change_rate:+.1f}%")
            
            logger.info(f"SQL {sql_template.sql_id} 扫描行数变化评分调整: +{score_adjustment}分")
        
        return {
            'score_adjustment': score_adjustment,
            'change_risk_level': change_risk_level,
            'change_analysis': change_analysis,
            'scan_change_data': scan_change_data
        }
    
    @classmethod
    def analyze_sql_with_scan_changes(cls, sql_template, cpu_cores=None):
        """增强版的SQL分析，包含扫描行数变化检查
        
        这个方法扩展了原有的analyze_sql方法，加入了扫描行数变化的分析
        
        Args:
            sql_template: SQLTemplate对象
            cpu_cores: RDS实例的CPU核数
            
        Returns:
            SQLAuditScore: 分析结果对象
        """
        from loguru import logger
        
        # 1. 执行原有的SQL分析
        audit_score = cls.analyze_sql(sql_template, cpu_cores)
        
        # 2. 进行扫描行数变化分析
        scan_changes = cls.integrate_scan_changes_to_score(sql_template, cpu_cores=cpu_cores)
        
        # 3. 更新评分和分析结果
        if scan_changes['score_adjustment'] > 0:
            # 调整风险评分
            new_risk_score = min(100, audit_score.risk_score + scan_changes['score_adjustment'])
            
            # 更新分析结果
            existing_analysis = audit_score.analysis_result
            new_analysis = scan_changes['change_analysis']
            
            if new_analysis:
                updated_analysis = existing_analysis
                if existing_analysis and not existing_analysis.endswith('\n'):
                    updated_analysis += '\n'
                updated_analysis += '\n'.join(new_analysis)
                
                # 更新数据库记录
                audit_score.risk_score = new_risk_score
                audit_score.analysis_result = updated_analysis
                audit_score.save()
                
                logger.info(f"SQL {sql_template.sql_id} 评分已更新，包含扫描行数变化分析")
        
        return audit_score 