from django.db import models
from django.utils import timezone
from django.conf import settings
from accounts.models import User


class AliCloudRegion(models.Model):
    """阿里云地域"""
    region_id = models.CharField(max_length=50, unique=True, verbose_name="地域ID")
    region_name = models.CharField(max_length=100, verbose_name="地域名称")
    
    def __str__(self):
        return f"{self.region_name} ({self.region_id})"
    
    class Meta:
        verbose_name = "阿里云地域"
        verbose_name_plural = verbose_name


class RDSInstanceSpec(models.Model):
    """RDS实例规格对应表"""
    instance_class = models.CharField(max_length=50, unique=True, verbose_name="实例规格")
    series = models.CharField(max_length=50, verbose_name="系列", help_text="如基础系列、高可用系列等")
    cpu_cores = models.IntegerField(verbose_name="CPU核数")
    memory_size = models.IntegerField(verbose_name="内存大小(GB)")
    max_connections = models.IntegerField(verbose_name="最大连接数", default=0)
    max_iops = models.IntegerField(verbose_name="最大IOPS", default=0)
    description = models.TextField(blank=True, null=True, verbose_name="规格描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "RDS实例规格"
        verbose_name_plural = verbose_name
        ordering = ['cpu_cores', 'memory_size']
        
    def __str__(self):
        return f"{self.instance_class} ({self.cpu_cores}核 {self.memory_size}GB)"


class RDSInstance(models.Model):
    """RDS实例"""
    # 实例状态选项
    STATUS_CHOICES = (
        ('Running', '运行中'),
        ('Creating', '创建中'),
        ('Stopped', '已停止'),
        ('Rebooting', '重启中'),
        ('DBInstanceClassChanging', '升降配中'),
        ('TRANSING', '迁移中'),
        ('EngineVersionUpgrading', '升级中'),
        ('TransingToOthers', '迁移他人中'),
        ('GuardDBInstanceCreating', '创建灾备实例中'),
        ('Restoring', '备份恢复中'),
        ('Importing', '数据导入中'),
        ('ImportingFromOthers', '从其他实例导入中'),
        ('TempDBInstanceCreating', '创建临时实例中'),
        ('Unavailable', '不可用'),
        ('ReadOnlyExpanding', '只读扩展中'),
    )
    
    instance_id = models.CharField(max_length=100, unique=True, verbose_name="实例ID")
    instance_name = models.CharField(max_length=100, verbose_name="实例名称")
    instance_type = models.CharField(max_length=50, verbose_name="实例类型")
    instance_class = models.CharField(max_length=50, verbose_name="实例规格")
    engine = models.CharField(max_length=50, verbose_name="数据库引擎")
    engine_version = models.CharField(max_length=20, verbose_name="引擎版本")
    status = models.CharField(max_length=50, choices=STATUS_CHOICES, verbose_name="实例状态")
    region_id = models.IntegerField(default='', verbose_name="所属地域ID")
    zone_id = models.CharField(max_length=50, verbose_name="可用区")
    connection_string = models.CharField(max_length=200, verbose_name="连接地址")
    port = models.IntegerField(default=3306, verbose_name="端口")
    vpc_id = models.CharField(max_length=100, blank=True, null=True, verbose_name="VPC ID")
    description = models.TextField(blank=True, null=True, verbose_name="实例描述")
    create_time = models.DateTimeField(blank=True, null=True, verbose_name="创建时间")
    expire_time = models.DateTimeField(blank=True, null=True, verbose_name="到期时间")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="记录创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="记录更新时间")
    last_sync_time = models.DateTimeField(blank=True, null=True, verbose_name="最后同步时间")
    
    def __str__(self):
        return f"{self.instance_name} ({self.instance_id})"
    
    class Meta:
        verbose_name = "RDS实例"
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        
    @property
    def region(self):
        """获取关联的地域对象"""
        try:
            return AliCloudRegion.objects.get(id=self.region_id)
        except AliCloudRegion.DoesNotExist:
            return None
            
    @property
    def spec(self):
        """获取实例规格详情"""
        try:
            return RDSInstanceSpec.objects.get(instance_class=self.instance_class)
        except RDSInstanceSpec.DoesNotExist:
            return None
            
    @property
    def cpu_cores(self):
        """获取CPU核数"""
        spec = self.spec
        return spec.cpu_cores if spec else None
        
    @property
    def memory_size(self):
        """获取内存大小(GB)"""
        spec = self.spec
        return spec.memory_size if spec else None
        
    @property
    def max_connections(self):
        """获取最大连接数"""
        spec = self.spec
        return spec.max_connections if spec else None
        
    @property
    def max_iops(self):
        """获取最大IOPS"""
        spec = self.spec
        return spec.max_iops if spec else None


class Database(models.Model):
    """数据库"""
    instance_id = models.CharField(max_length=100, default='default-instance', verbose_name="所属实例ID")
    name = models.CharField(max_length=100, verbose_name="数据库名称")
    character_set = models.CharField(max_length=50, verbose_name="字符集")
    description = models.TextField(blank=True, null=True, verbose_name="描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "数据库"
        verbose_name_plural = verbose_name
        unique_together = [['instance_id', 'name']]
    
    def __str__(self):
        return f"{self.name} ({self.instance_id})"
        
    @property
    def instance(self):
        """提供兼容旧代码的实例访问方法"""
        try:
            return RDSInstance.objects.get(instance_id=self.instance_id)
        except RDSInstance.DoesNotExist:
            return None


class DBAccount(models.Model):
    """数据库账号"""
    instance_id = models.CharField(max_length=100, default='default-instance', verbose_name="所属实例ID")
    account_name = models.CharField(max_length=100, verbose_name="账号名称")
    account_type = models.CharField(max_length=50, verbose_name="账号类型")
    account_status = models.CharField(max_length=50, verbose_name="账号状态")
    description = models.TextField(blank=True, null=True, verbose_name="描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "数据库账号"
        verbose_name_plural = verbose_name
        unique_together = [['instance_id', 'account_name']]
    
    def __str__(self):
        return f"{self.account_name} ({self.instance_id})"
        
    @property
    def instance(self):
        """提供兼容旧代码的实例访问方法"""
        try:
            return RDSInstance.objects.get(instance_id=self.instance_id)
        except RDSInstance.DoesNotExist:
            return None


class DBAccountPrivilege(models.Model):
    """数据库账号权限"""
    PRIVILEGE_CHOICES = (
        ('ReadOnly', '只读'),
        ('ReadWrite', '读写'),
        ('DDLOnly', '仅DDL'),
        ('DMLOnly', '仅DML'),
    )
    
    instance_id = models.CharField(max_length=100, default='default-instance', verbose_name="所属实例ID")
    account_name = models.CharField(max_length=100, default='', verbose_name="账号名称")
    database_name = models.CharField(max_length=100, default='', verbose_name="数据库名称")
    privilege = models.CharField(max_length=50, choices=PRIVILEGE_CHOICES, verbose_name="权限类型")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "账号权限"
        verbose_name_plural = verbose_name
        unique_together = [['instance_id', 'account_name', 'database_name']]
    
    def __str__(self):
        return f"{self.account_name} - {self.database_name} ({self.privilege})"
        
    @property
    def account(self):
        """提供兼容旧代码的账号访问方法"""
        try:
            return DBAccount.objects.get(instance_id=self.instance_id, account_name=self.account_name)
        except DBAccount.DoesNotExist:
            return None
            
    @property
    def database(self):
        """提供兼容旧代码的数据库访问方法"""
        try:
            return Database.objects.get(instance_id=self.instance_id, name=self.database_name)
        except Database.DoesNotExist:
            return None


class InstanceAccessPermission(models.Model):
    """实例访问权限 - 控制用户对实例的访问权限"""
    user_id = models.IntegerField(default='', verbose_name="用户ID")
    instance_string_id = models.CharField(max_length=100, default='', verbose_name="实例ID")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "实例访问权限"
        verbose_name_plural = verbose_name
        unique_together = [['user_id', 'instance_string_id']]
    
    def __str__(self):
        user = self.user
        instance = self.instance
        if user and instance:
            return f"{user.username} - {instance.instance_name}"
        return f"Permission {self.id}"
        
    @property
    def user(self):
        """获取关联的用户对象"""
        try:
            return User.objects.get(id=self.user_id)
        except User.DoesNotExist:
            return None
            
    @property
    def instance(self):
        """获取关联的实例对象"""
        try:
            return RDSInstance.objects.get(instance_id=self.instance_string_id)
        except RDSInstance.DoesNotExist:
            return None


class OperationLog(models.Model):
    """操作日志"""
    OPERATION_TYPE_CHOICES = (
        ('sync_instances', '同步实例'),
        ('create_database', '创建数据库'),
        ('delete_database', '删除数据库'),
        ('create_account', '创建账号'),
        ('delete_account', '删除账号'),
        ('modify_privilege', '修改权限'),
        ('other', '其他操作'),
    )
    
    user_id = models.IntegerField(null=True, blank=True, verbose_name="操作用户ID")
    instance_string_id = models.CharField(max_length=100, null=True, blank=True, verbose_name="实例ID")
    operation_type = models.CharField(max_length=50, choices=OPERATION_TYPE_CHOICES, verbose_name="操作类型")
    operation_detail = models.TextField(verbose_name="操作详情")
    operation_time = models.DateTimeField(default=timezone.now, verbose_name="操作时间")
    operation_status = models.BooleanField(default=True, verbose_name="操作状态")
    
    class Meta:
        verbose_name = "操作日志"
        verbose_name_plural = verbose_name
        ordering = ['-operation_time']
    
    def __str__(self):
        return f"{self.operation_type} - {self.operation_time.strftime('%Y-%m-%d %H:%M:%S')}"
        
    @property
    def user(self):
        """获取关联的用户对象"""
        if not self.user_id:
            return None
        try:
            return User.objects.get(id=self.user_id)
        except User.DoesNotExist:
            return None
            
    @property
    def instance(self):
        """获取关联的实例对象"""
        if not self.instance_string_id:
            return None
        try:
            return RDSInstance.objects.get(instance_id=self.instance_string_id)
        except RDSInstance.DoesNotExist:
            return None


class SlowQueryLog(models.Model):
    """慢查询日志"""
    instance_id = models.CharField(max_length=100, default='default-instance', verbose_name="实例ID")
    database_name = models.CharField(max_length=100, verbose_name="数据库名称")
    sql_hash = models.CharField(max_length=64, default='', verbose_name="SQL哈希值")
    sql_count = models.IntegerField(verbose_name="执行次数", default=0)
    parse_row_count = models.BigIntegerField(verbose_name="解析行数", default=0)
    return_row_count = models.BigIntegerField(verbose_name="返回行数", default=0)
    max_exe_time = models.IntegerField(verbose_name="最大执行时间(毫秒)", default=0)
    max_lock_time = models.IntegerField(verbose_name="最大锁定时间(毫秒)", default=0)
    timezone = models.DateField(verbose_name="统计日期", default=timezone.now)
    sql_text = models.TextField(verbose_name="SQL语句")
    risk_level = models.IntegerField(verbose_name="风险等级", default=0)  # 0-低风险 1-中风险 2-高风险
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="记录创建时间")
    
    class Meta:
        verbose_name = "慢查询日志"
        verbose_name_plural = verbose_name
        ordering = ['-timezone', '-max_exe_time']
        indexes = [
            models.Index(fields=['timezone'], name='rds_manager_timezone_idx'),
            models.Index(fields=['created_at'], name='rds_manager_created_at_idx'),
            models.Index(fields=['instance_id', 'database_name', 'timezone'], name='rds_inst_db_timezone_idx'),
        ]
        unique_together = [('sql_hash', 'instance_id', 'timezone')]
    
    def __str__(self):
        return f"{self.database_name} - {self.max_exe_time}毫秒 - {self.timezone}"
    
    @property
    def instance(self):
        """获取关联的实例对象"""
        try:
            return RDSInstance.objects.get(instance_id=self.instance_id)
        except RDSInstance.DoesNotExist:
            return None


class SlowQueryAnalysis(models.Model):
    """慢查询分析结果"""
    sql_hash = models.CharField(max_length=64, verbose_name="SQL哈希值", default='')
    instance_id = models.CharField(max_length=100, default='default-instance', verbose_name="实例ID")
    timezone = models.DateField(verbose_name="统计日期", default=timezone.now)
    table_scans = models.BooleanField(default=False, verbose_name="全表扫描")
    no_indexes = models.BooleanField(default=False, verbose_name="无索引")
    poor_indexes = models.BooleanField(default=False, verbose_name="索引不合理")
    temporary_tables = models.BooleanField(default=False, verbose_name="临时表")
    filesort = models.BooleanField(default=False, verbose_name="文件排序")
    analysis_result = models.TextField(verbose_name="分析结果")
    optimization_suggestion = models.TextField(verbose_name="优化建议")
    risk_score = models.IntegerField(verbose_name="风险评分", default=0)
    query_count_score = models.IntegerField(verbose_name="查询次数评分", default=0)
    query_time_score = models.IntegerField(verbose_name="查询时间评分", default=0)
    scan_rows_score = models.IntegerField(verbose_name="扫描行数评分", default=0)
    affected_rows_score = models.IntegerField(verbose_name="影响行数评分", default=0)
    lock_time_score = models.IntegerField(verbose_name="锁等待时间评分", default=0)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="分析时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "慢查询分析"
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        unique_together = [('sql_hash', 'instance_id', 'timezone')]
    
    def __str__(self):
        return f"分析 {self.sql_hash} ({self.instance_id})"
    
    @property
    def slow_query(self):
        """获取关联的慢查询日志对象"""
        try:
            return SlowQueryLog.objects.get(
                sql_hash=self.sql_hash,
                instance_id=self.instance_id,
                timezone=self.timezone
            )
        except SlowQueryLog.DoesNotExist:
            return None


class SlowQueryScoreConfig(models.Model):
    """慢查询评分配置"""
    CURVE_CHOICES = (
        ('linear', '线性曲线'),
        ('exponential', '指数曲线'),
        ('logarithmic', '对数曲线'),
        ('sine', '正弦曲线'),
        ('polynomial', '多项式曲线'),
    )

    CONFIG_TYPE_CHOICES = (
        ('slowlog', '慢查询'),
        ('sqltemplate', 'SQL模板'),
    )

    # 配置名称和描述
    name = models.CharField(max_length=100, verbose_name="配置名称")
    config_type = models.CharField(max_length=16, choices=CONFIG_TYPE_CHOICES, default='slowlog', verbose_name="配置类型")
    description = models.TextField(blank=True, null=True, verbose_name="配置描述")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    
    # 查询次数参数
    query_count_min = models.IntegerField(default=5, verbose_name="查询次数最小值")
    query_count_max = models.IntegerField(default=500, verbose_name="查询次数最大值")
    query_count_weight = models.FloatField(default=0.4, verbose_name="查询次数权重")
    query_count_curve = models.CharField(max_length=20, choices=CURVE_CHOICES, default='linear', verbose_name="查询次数曲线")
    
    # 查询时间参数
    query_time_min = models.IntegerField(default=0, verbose_name="查询时间最小值(毫秒)")
    query_time_max = models.IntegerField(default=10000, verbose_name="查询时间最大值(毫秒)")
    query_time_weight = models.FloatField(default=0.3, verbose_name="查询时间权重")
    query_time_curve = models.CharField(max_length=20, choices=CURVE_CHOICES, default='sine', verbose_name="查询时间曲线")
    
    # 扫描行数参数
    scan_rows_min = models.IntegerField(default=1, verbose_name="扫描行数最小值")
    scan_rows_max = models.IntegerField(default=1000000, verbose_name="扫描行数最大值")
    scan_rows_weight = models.FloatField(default=0.2, verbose_name="扫描行数权重")
    scan_rows_curve = models.CharField(max_length=20, choices=CURVE_CHOICES, default='polynomial', verbose_name="扫描行数曲线")
    
    # 影响行数参数
    affected_rows_min = models.IntegerField(default=0, verbose_name="影响行数最小值")
    affected_rows_max = models.IntegerField(default=500, verbose_name="影响行数最大值")
    affected_rows_weight = models.FloatField(default=0.05, verbose_name="影响行数权重")
    affected_rows_curve = models.CharField(max_length=20, choices=CURVE_CHOICES, default='linear', verbose_name="影响行数曲线")
    
    # 锁等待时间参数
    lock_time_min = models.IntegerField(default=0, verbose_name="锁等待时间最小值(毫秒)")
    lock_time_max = models.IntegerField(default=1000, verbose_name="锁等待时间最大值(毫秒)")
    lock_time_weight = models.FloatField(default=0.05, verbose_name="锁等待时间权重")
    lock_time_curve = models.CharField(max_length=20, choices=CURVE_CHOICES, default='exponential', verbose_name="锁等待时间曲线")
    
    # 多项式曲线系数（仅用于多项式曲线）
    polynomial_a = models.FloatField(default=0.0, verbose_name="多项式系数a")
    polynomial_b = models.FloatField(default=0.02, verbose_name="多项式系数b")
    polynomial_c = models.FloatField(default=0.001, verbose_name="多项式系数c")
    polynomial_d = models.FloatField(default=0.0005, verbose_name="多项式系数d")
    polynomial_e = models.FloatField(default=0.00001, verbose_name="多项式系数e")
    polynomial_f = models.FloatField(default=0.000001, verbose_name="多项式系数f")
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "评分配置"
        verbose_name_plural = verbose_name
        ordering = ['-created_at']

    def __str__(self):
        config_type_display = dict(self.CONFIG_TYPE_CHOICES).get(self.config_type, self.config_type)
        return f"{self.name} ({config_type_display}) - {self.created_at.strftime('%Y-%m-%d')}"
    
    @staticmethod
    def get_active_config(config_type='slowlog'):
        """获取指定类型的激活配置"""
        config = SlowQueryScoreConfig.objects.filter(
            is_active=True,
            config_type=config_type
        ).first()
        if not config:
            # 如果没有激活的配置，则创建默认配置
            config_name = "默认慢查询配置" if config_type == 'slowlog' else "默认SQL模板配置"
            config_desc = "系统默认慢查询配置" if config_type == 'slowlog' else "系统默认SQL模板配置"
            config = SlowQueryScoreConfig.objects.create(
                name=config_name,
                config_type=config_type,
                description=config_desc,
                is_active=True
            )
        return config

    def save(self, *args, **kwargs):
        """保存时确保同一类型只有一个激活配置"""
        if self.is_active:
            # 如果当前配置被激活，则将同类型的其他配置设为非激活
            SlowQueryScoreConfig.objects.filter(
                config_type=self.config_type,
                is_active=True
            ).exclude(pk=self.pk).update(is_active=False)
        super().save(*args, **kwargs)
