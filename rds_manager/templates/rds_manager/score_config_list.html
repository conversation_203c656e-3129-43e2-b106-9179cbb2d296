{% extends 'rds_manager/base.html' %}
{% load static %}

{% block title %}评分配置管理 - 阿里云RDS管理平台{% endblock %}

{% block extra_css %}
<style>
    .config-card {
        transition: all 0.3s ease;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .config-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    .active-badge {
        position: absolute;
        top: 0;
        right: 0;
        margin: 10px;
    }
    .card-footer {
        background-color: transparent;
        border-top: 1px solid rgba(0,0,0,0.05);
    }
    .config-description {
        height: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
    }
    .empty-state {
        text-align: center;
        padding: 3rem 0;
    }
    .empty-state .icon {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }
    .empty-state .message {
        color: #6c757d;
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
        <h1 class="h3">
            <i class="fas fa-sliders-h text-primary"></i>
            评分配置管理
            <small class="text-muted fs-6">慢查询 & SQL模板评分配置</small>
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{% url 'rds_manager:score_config_new' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> 新增配置
            </a>
        </div>
    </div>
    
    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if configs %}
    <div class="row">
        {% for config in configs %}
        <div class="col-md-4 mb-4">
            <div class="card h-100 config-card position-relative {% if config.is_active %}border-primary{% endif %}">
                {% if config.is_active %}
                <span class="badge bg-primary active-badge">当前生效</span>
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title">
                        {{ config.name }}
                        <small class="text-muted">
                            ({{ config.created_at|date:"Y-m-d" }})
                        </small>
                        <span class="badge bg-{% if config.config_type == 'slowlog' %}info{% else %}warning{% endif %} ms-2">
                            {% if config.config_type == 'slowlog' %}慢查询{% else %}SQL模板{% endif %}
                        </span>
                    </h5>
                    <div class="config-description">
                        <p class="card-text text-muted">{{ config.description|default:"无描述" }}</p>
                    </div>
                    <hr>
                    <div class="row mb-2">
                        <div class="col-6">
                            <div class="text-muted small">查询次数权重</div>
                            <div class="font-weight-bold">{{ config.query_count_weight|floatformat:2 }} ({{ config.query_count_curve }})</div>
                        </div>
                        <div class="col-6">
                            <div class="text-muted small">查询时间权重</div>
                            <div class="font-weight-bold">{{ config.query_time_weight|floatformat:2 }} ({{ config.query_time_curve }})</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-muted small">扫描行数权重</div>
                            <div class="font-weight-bold">{{ config.scan_rows_weight|floatformat:2 }} ({{ config.scan_rows_curve }})</div>
                        </div>
                        <div class="col-6">
                            <div class="text-muted small">影响行数权重</div>
                            <div class="font-weight-bold">{{ config.affected_rows_weight|floatformat:2 }} ({{ config.affected_rows_curve }})</div>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <div>
                        {% if not config.is_active %}
                        <button class="btn btn-sm btn-outline-primary activate-btn" data-config-id="{{ config.id }}">
                            <i class="fas fa-check-circle"></i> 激活配置
                        </button>
                        {% endif %}
                    </div>
                    <div>
                        <a href="{% url 'rds_manager:score_config_detail' config.id %}" class="btn btn-sm btn-info">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                        {% if not config.is_active %}
                        <button class="btn btn-sm btn-danger delete-btn" data-config-id="{{ config.id }}" data-config-name="{{ config.name }}">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="empty-state">
        <div class="icon">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="message">
            <p>暂无评分配置</p>
            <p class="mt-3">
                <a href="{% url 'rds_manager:score_config_new' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> 创建新配置
                </a>
            </p>
        </div>
    </div>
    {% endif %}
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除配置 <span id="configNameSpan" class="fw-bold"></span> 吗？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 激活配置
    document.querySelectorAll('.activate-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const configId = this.getAttribute('data-config-id');
            
            fetch(`/rds/score-configs/${configId}/activate/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert('激活配置失败: ' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('激活配置错误:', error);
                alert('操作失败，请重试');
            });
        });
    });
    
    // 删除配置
    let configIdToDelete = null;
    
    document.querySelectorAll('.delete-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            configIdToDelete = this.getAttribute('data-config-id');
            const configName = this.getAttribute('data-config-name');
            document.getElementById('configNameSpan').textContent = configName;
            
            // 显示确认模态框
            const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
            modal.show();
        });
    });
    
    // 确认删除
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (!configIdToDelete) return;
        
        fetch(`/rds/score-configs/${configIdToDelete}/delete/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert('删除配置失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('删除配置错误:', error);
            alert('操作失败，请重试');
        });
    });
    
    // 获取CSRF Token
    function getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }
});
</script>
{% endblock %} 