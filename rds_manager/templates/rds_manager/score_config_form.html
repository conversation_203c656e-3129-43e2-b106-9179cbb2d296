{% extends "rds_manager/base.html" %}
{% load static humanize %}

{% block title %}{% if is_new %}新建{% else %}编辑{% endif %}慢查询评分配置 - 阿里云RDS管理平台{% endblock %}

{% block extra_css %}
<style>
    /* 表单区域样式 */
    .form-section {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        padding: 20px;
        margin-bottom: 25px;
        transition: all 0.3s ease;
    }
    
    .form-section:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .form-section h4 {
        color: #3f6ad8;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 10px;
        margin-bottom: 20px;
        font-weight: 600;
    }
    
    /* 曲线选择器样式 */
    .curve-option {
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 10px;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 0.85rem;
    }
    
    .curve-option:hover {
        border-color: #adb5bd;
        background-color: #f8f9fa;
    }
    
    .curve-option.active {
        border-color: #3f6ad8;
        background-color: #e7f1ff;
        box-shadow: 0 0 0 3px rgba(63, 106, 216, 0.2);
    }
    
    .curve-icon {
        font-size: 1.5rem;
        margin-bottom: 5px;
        color: #6c757d;
    }
    
    .curve-option.active .curve-icon {
        color: #3f6ad8;
    }
    
    /* 多项式系数区域 */
    .polynomial-coefficients {
        display: none;
    }
    
    .polynomial-coefficients.show {
        display: block;
        animation: fadeIn 0.5s;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    /* 必填字段标记 */
    .form-label.required:after {
        content: " *";
        color: #dc3545;
    }
    
    /* 测试结果区域 */
    #testResultSection {
        display: none;
    }
    
    .test-form {
        background-color: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        height: 100%;
    }
    
    .test-form h5, #testResults h5 {
        color: #495057;
        margin-bottom: 15px;
        font-weight: 600;
        font-size: 1rem;
    }
    
    #testResults {
        background-color: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        height: 100%;
    }
    
    /* 图表容器 */
    .score-chart {
        height: 350px;
        margin-top: 20px;
        background-color: #fff;
        border-radius: 6px;
        padding: 15px;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
        <h1 class="h3">
            <i class="fas fa-sliders-h text-primary"></i>
            {% if is_new %}创建新{% else %}编辑{% endif %}慢查询评分配置
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{% url 'rds_manager:score_config_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> 返回列表
            </a>
        </div>
    </div>
    
    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <form method="post" id="configForm">
        {% csrf_token %}
        
        <!-- 基本信息 -->
        <div class="form-section">
            <h4><i class="fas fa-info-circle me-2"></i>基本信息</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="name" class="form-label required">配置名称</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ config.name|default:'' }}" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="config_type" class="form-label required">配置类型</label>
                        <select class="form-select" id="config_type" name="config_type" required>
                            <option value="slowlog" {% if config.config_type == 'slowlog' or not config.config_type %}selected{% endif %}>慢查询</option>
                            <option value="sqltemplate" {% if config.config_type == 'sqltemplate' %}selected{% endif %}>SQL模板</option>
                        </select>
                        <small class="text-muted">选择此配置用于慢查询分析还是SQL模板分析</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3 form-check mt-4">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {% if config.is_active %}checked{% endif %}>
                        <label class="form-check-label" for="is_active">激活此配置</label>
                        <small class="form-text text-muted d-block">同一类型只能有一个激活配置</small>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="description" class="form-label">配置描述</label>
                <textarea class="form-control" id="description" name="description" rows="3">{{ config.description|default:'' }}</textarea>
            </div>
        </div>
        
        <!-- 查询次数参数 -->
        <div class="form-section">
            <h4><i class="fas fa-sync-alt me-2"></i>查询次数参数</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="query_count_min" class="form-label required">最小值（次）</label>
                        <input type="number" class="form-control" id="query_count_min" name="query_count_min" 
                               value="{{ config.query_count_min|default:5 }}" min="0" required>
                        <small class="text-muted">低于此值的查询次数评分为0</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="query_count_max" class="form-label required">最大值（次）</label>
                        <input type="number" class="form-control" id="query_count_max" name="query_count_max" 
                               value="{{ config.query_count_max|default:500 }}" min="1" required>
                        <small class="text-muted">高于此值的查询次数评分为100</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="query_count_weight" class="form-label required">权重</label>
                        <input type="number" class="form-control" id="query_count_weight" name="query_count_weight" 
                               value="{{ config.query_count_weight|default:0.4 }}" min="0" max="1" step="0.01" required>
                        <small class="text-muted">权重总和必须为1</small>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <label class="form-label required">评分曲线</label>
                    <input type="hidden" id="query_count_curve" name="query_count_curve" 
                           value="{{ config.query_count_curve|default:'linear' }}">
                    <div class="row">
                        {% for curve_id, curve_name in curve_choices %}
                        <div class="col-md-2 mb-3">
                            <div class="curve-option {% if config.query_count_curve == curve_id or not config and curve_id == 'linear' %}active{% endif %}" 
                                 data-curve="{{ curve_id }}" data-target="query_count_curve">
                                <div class="curve-icon">
                                    {% if curve_id == 'linear' %}
                                    <i class="fas fa-long-arrow-alt-up"></i>
                                    {% elif curve_id == 'exponential' %}
                                    <i class="fas fa-chart-line"></i>
                                    {% elif curve_id == 'logarithmic' %}
                                    <i class="fas fa-compress-alt"></i>
                                    {% elif curve_id == 'sine' %}
                                    <i class="fas fa-wave-square"></i>
                                    {% elif curve_id == 'polynomial' %}
                                    <i class="fas fa-chart-area"></i>
                                    {% endif %}
                                </div>
                                <div>{{ curve_name }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 查询时间参数 -->
        <div class="form-section">
            <h4><i class="fas fa-hourglass-half me-2"></i>查询时间参数</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="query_time_min" class="form-label required">最小值（毫秒）</label>
                        <input type="number" class="form-control" id="query_time_min" name="query_time_min" 
                               value="{{ config.query_time_min|default:0 }}" min="0" required>
                        <small class="text-muted">低于此值的查询时间评分为0</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="query_time_max" class="form-label required">最大值（毫秒）</label>
                        <input type="number" class="form-control" id="query_time_max" name="query_time_max" 
                               value="{{ config.query_time_max|default:10000 }}" min="1" required>
                        <small class="text-muted">高于此值的查询时间评分为100</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="query_time_weight" class="form-label required">权重</label>
                        <input type="number" class="form-control" id="query_time_weight" name="query_time_weight" 
                               value="{{ config.query_time_weight|default:0.3 }}" min="0" max="1" step="0.01" required>
                        <small class="text-muted">权重总和必须为1</small>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <label class="form-label required">评分曲线</label>
                    <input type="hidden" id="query_time_curve" name="query_time_curve" 
                           value="{{ config.query_time_curve|default:'sine' }}">
                    <div class="row">
                        {% for curve_id, curve_name in curve_choices %}
                        <div class="col-md-2 mb-3">
                            <div class="curve-option {% if config.query_time_curve == curve_id or not config and curve_id == 'sine' %}active{% endif %}" 
                                 data-curve="{{ curve_id }}" data-target="query_time_curve">
                                <div class="curve-icon">
                                    {% if curve_id == 'linear' %}
                                    <i class="fas fa-long-arrow-alt-up"></i>
                                    {% elif curve_id == 'exponential' %}
                                    <i class="fas fa-chart-line"></i>
                                    {% elif curve_id == 'logarithmic' %}
                                    <i class="fas fa-compress-alt"></i>
                                    {% elif curve_id == 'sine' %}
                                    <i class="fas fa-wave-square"></i>
                                    {% elif curve_id == 'polynomial' %}
                                    <i class="fas fa-chart-area"></i>
                                    {% endif %}
                                </div>
                                <div>{{ curve_name }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 扫描行数参数 -->
        <div class="form-section">
            <h4><i class="fas fa-search me-2"></i>扫描行数参数</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="scan_rows_min" class="form-label required">最小值（行）</label>
                        <input type="number" class="form-control" id="scan_rows_min" name="scan_rows_min" 
                               value="{{ config.scan_rows_min|default:1 }}" min="0" required>
                        <small class="text-muted">低于此值的扫描行数评分为0</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="scan_rows_max" class="form-label required">最大值（行）</label>
                        <input type="number" class="form-control" id="scan_rows_max" name="scan_rows_max" 
                               value="{{ config.scan_rows_max|default:1000000 }}" min="1" required>
                        <small class="text-muted">高于此值的扫描行数评分为100</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="scan_rows_weight" class="form-label required">权重</label>
                        <input type="number" class="form-control" id="scan_rows_weight" name="scan_rows_weight" 
                               value="{{ config.scan_rows_weight|default:0.2 }}" min="0" max="1" step="0.01" required>
                        <small class="text-muted">权重总和必须为1</small>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <label class="form-label required">评分曲线</label>
                    <input type="hidden" id="scan_rows_curve" name="scan_rows_curve" 
                           value="{{ config.scan_rows_curve|default:'polynomial' }}">
                    <div class="row">
                        {% for curve_id, curve_name in curve_choices %}
                        <div class="col-md-2 mb-3">
                            <div class="curve-option {% if config.scan_rows_curve == curve_id or not config and curve_id == 'polynomial' %}active{% endif %}" 
                                 data-curve="{{ curve_id }}" data-target="scan_rows_curve">
                                <div class="curve-icon">
                                    {% if curve_id == 'linear' %}
                                    <i class="fas fa-long-arrow-alt-up"></i>
                                    {% elif curve_id == 'exponential' %}
                                    <i class="fas fa-chart-line"></i>
                                    {% elif curve_id == 'logarithmic' %}
                                    <i class="fas fa-compress-alt"></i>
                                    {% elif curve_id == 'sine' %}
                                    <i class="fas fa-wave-square"></i>
                                    {% elif curve_id == 'polynomial' %}
                                    <i class="fas fa-chart-area"></i>
                                    {% endif %}
                                </div>
                                <div>{{ curve_name }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 影响行数参数 -->
        <div class="form-section">
            <h4><i class="fas fa-table me-2"></i>影响行数参数</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="affected_rows_min" class="form-label required">最小值（行）</label>
                        <input type="number" class="form-control" id="affected_rows_min" name="affected_rows_min" 
                               value="{{ config.affected_rows_min|default:0 }}" min="0" required>
                        <small class="text-muted">低于此值的影响行数评分为0</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="affected_rows_max" class="form-label required">最大值（行）</label>
                        <input type="number" class="form-control" id="affected_rows_max" name="affected_rows_max" 
                               value="{{ config.affected_rows_max|default:500 }}" min="1" required>
                        <small class="text-muted">高于此值的影响行数评分为100</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="affected_rows_weight" class="form-label required">权重</label>
                        <input type="number" class="form-control" id="affected_rows_weight" name="affected_rows_weight" 
                               value="{{ config.affected_rows_weight|default:0.05 }}" min="0" max="1" step="0.01" required>
                        <small class="text-muted">权重总和必须为1</small>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <label class="form-label required">评分曲线</label>
                    <input type="hidden" id="affected_rows_curve" name="affected_rows_curve" 
                           value="{{ config.affected_rows_curve|default:'linear' }}">
                    <div class="row">
                        {% for curve_id, curve_name in curve_choices %}
                        <div class="col-md-2 mb-3">
                            <div class="curve-option {% if config.affected_rows_curve == curve_id or not config and curve_id == 'linear' %}active{% endif %}" 
                                 data-curve="{{ curve_id }}" data-target="affected_rows_curve">
                                <div class="curve-icon">
                                    {% if curve_id == 'linear' %}
                                    <i class="fas fa-long-arrow-alt-up"></i>
                                    {% elif curve_id == 'exponential' %}
                                    <i class="fas fa-chart-line"></i>
                                    {% elif curve_id == 'logarithmic' %}
                                    <i class="fas fa-compress-alt"></i>
                                    {% elif curve_id == 'sine' %}
                                    <i class="fas fa-wave-square"></i>
                                    {% elif curve_id == 'polynomial' %}
                                    <i class="fas fa-chart-area"></i>
                                    {% endif %}
                                </div>
                                <div>{{ curve_name }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 锁等待时间参数 -->
        <div class="form-section">
            <h4><i class="fas fa-lock me-2"></i>锁等待时间参数</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="lock_time_min" class="form-label required">最小值（毫秒）</label>
                        <input type="number" class="form-control" id="lock_time_min" name="lock_time_min" 
                               value="{{ config.lock_time_min|default:0 }}" min="0" required>
                        <small class="text-muted">低于此值的锁等待时间评分为0</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="lock_time_max" class="form-label required">最大值（毫秒）</label>
                        <input type="number" class="form-control" id="lock_time_max" name="lock_time_max" 
                               value="{{ config.lock_time_max|default:1000 }}" min="1" required>
                        <small class="text-muted">高于此值的锁等待时间评分为100</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="lock_time_weight" class="form-label required">权重</label>
                        <input type="number" class="form-control" id="lock_time_weight" name="lock_time_weight" 
                               value="{{ config.lock_time_weight|default:0.05 }}" min="0" max="1" step="0.01" required>
                        <small class="text-muted">权重总和必须为1</small>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <label class="form-label required">评分曲线</label>
                    <input type="hidden" id="lock_time_curve" name="lock_time_curve" 
                           value="{{ config.lock_time_curve|default:'exponential' }}">
                    <div class="row">
                        {% for curve_id, curve_name in curve_choices %}
                        <div class="col-md-2 mb-3">
                            <div class="curve-option {% if config.lock_time_curve == curve_id or not config and curve_id == 'exponential' %}active{% endif %}" 
                                 data-curve="{{ curve_id }}" data-target="lock_time_curve">
                                <div class="curve-icon">
                                    {% if curve_id == 'linear' %}
                                    <i class="fas fa-long-arrow-alt-up"></i>
                                    {% elif curve_id == 'exponential' %}
                                    <i class="fas fa-chart-line"></i>
                                    {% elif curve_id == 'logarithmic' %}
                                    <i class="fas fa-compress-alt"></i>
                                    {% elif curve_id == 'sine' %}
                                    <i class="fas fa-wave-square"></i>
                                    {% elif curve_id == 'polynomial' %}
                                    <i class="fas fa-chart-area"></i>
                                    {% endif %}
                                </div>
                                <div>{{ curve_name }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 多项式曲线系数 -->
        <div class="form-section polynomial-coefficients">
            <h4><i class="fas fa-calculator me-2"></i>多项式曲线系数</h4>
            <p class="text-muted mb-3">仅当任意一个参数使用多项式曲线时才需要填写，公式: Y = a + b·X + c·X² + d·X³ + e·X⁴ + f·X⁵</p>
            <div class="row">
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="polynomial_a" class="form-label">系数a</label>
                        <input type="number" class="form-control" id="polynomial_a" name="polynomial_a" 
                               value="{{ config.polynomial_a|default:0.0 }}" step="0.00001">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="polynomial_b" class="form-label">系数b</label>
                        <input type="number" class="form-control" id="polynomial_b" name="polynomial_b" 
                               value="{{ config.polynomial_b|default:0.02 }}" step="0.00001">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="polynomial_c" class="form-label">系数c</label>
                        <input type="number" class="form-control" id="polynomial_c" name="polynomial_c" 
                               value="{{ config.polynomial_c|default:0.001 }}" step="0.00001">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="polynomial_d" class="form-label">系数d</label>
                        <input type="number" class="form-control" id="polynomial_d" name="polynomial_d" 
                               value="{{ config.polynomial_d|default:0.0005 }}" step="0.00001">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="polynomial_e" class="form-label">系数e</label>
                        <input type="number" class="form-control" id="polynomial_e" name="polynomial_e" 
                               value="{{ config.polynomial_e|default:0.00001 }}" step="0.00001">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="polynomial_f" class="form-label">系数f</label>
                        <input type="number" class="form-control" id="polynomial_f" name="polynomial_f" 
                               value="{{ config.polynomial_f|default:0.000001 }}" step="0.00001">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 按钮区域 -->
        <div class="form-section d-flex justify-content-between">
            <button type="button" class="btn btn-info" id="testConfigBtn">
                <i class="fas fa-flask me-1"></i> 测试此配置
            </button>
            <div>
                <a href="{% url 'rds_manager:score_config_list' %}" class="btn btn-secondary me-2">取消</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> 保存配置
                </button>
            </div>
        </div>
    </form>
    
    <!-- 测试结果区域 -->
    <div id="testResultSection" class="form-section" style="display: none;">
        <h4><i class="fas fa-chart-bar me-2"></i>测试结果</h4>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="test-form">
                    <h5>输入测试数据</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_query_count" class="form-label">查询次数（次）</label>
                                <input type="number" class="form-control" id="test_query_count" value="50" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_query_time" class="form-label">查询时间（毫秒）</label>
                                <input type="number" class="form-control" id="test_query_time" value="1000" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="test_scan_rows" class="form-label">扫描行数（行）</label>
                                <input type="number" class="form-control" id="test_scan_rows" value="10000" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="test_affected_rows" class="form-label">影响行数（行）</label>
                                <input type="number" class="form-control" id="test_affected_rows" value="100" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="test_lock_time" class="form-label">锁等待时间（毫秒）</label>
                                <input type="number" class="form-control" id="test_lock_time" value="100" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-primary" id="runTestBtn">
                            <i class="fas fa-play me-1"></i> 运行测试
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div id="testResults">
                    <h5>得分明细</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>参数</th>
                                    <th>得分</th>
                                    <th>权重</th>
                                    <th>加权得分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>查询次数</td>
                                    <td><span id="query_count_score">-</span></td>
                                    <td><span id="query_count_weight_display">-</span></td>
                                    <td><span id="query_count_weighted_score">-</span></td>
                                </tr>
                                <tr>
                                    <td>查询时间</td>
                                    <td><span id="query_time_score">-</span></td>
                                    <td><span id="query_time_weight_display">-</span></td>
                                    <td><span id="query_time_weighted_score">-</span></td>
                                </tr>
                                <tr>
                                    <td>扫描行数</td>
                                    <td><span id="scan_rows_score">-</span></td>
                                    <td><span id="scan_rows_weight_display">-</span></td>
                                    <td><span id="scan_rows_weighted_score">-</span></td>
                                </tr>
                                <tr>
                                    <td>影响行数</td>
                                    <td><span id="affected_rows_score">-</span></td>
                                    <td><span id="affected_rows_weight_display">-</span></td>
                                    <td><span id="affected_rows_weighted_score">-</span></td>
                                </tr>
                                <tr>
                                    <td>锁等待时间</td>
                                    <td><span id="lock_time_score">-</span></td>
                                    <td><span id="lock_time_weight_display">-</span></td>
                                    <td><span id="lock_time_weighted_score">-</span></td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr class="table-primary">
                                    <th colspan="3">总分</th>
                                    <th><span id="total_score">-</span></th>
                                </tr>
                                <tr class="table-info">
                                    <th colspan="3">风险等级</th>
                                    <th><span id="risk_level">-</span></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <h5>曲线预览</h5>
                <div class="score-chart" id="curvePreview" style="height: 350px; width: 100%;"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 确保testUrl变量全局定义
window.testUrl = '{% if config %}{% url "rds_manager:score_config_test" config.id %}{% else %}{% url "rds_manager:score_config_test_temp" %}{% endif %}';

// 定义变量表示是否已创建了canvas元素
window.canvasCreated = false;

// 创建Canvas元素函数
function createCanvas() {
    if (window.canvasCreated) return;
    
    const container = document.getElementById('curvePreview');
    if (!container) return;
    
    // 清空容器
    container.innerHTML = '';
    
    // 创建canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'curveChart';
    canvas.width = container.offsetWidth;
    canvas.height = container.offsetHeight;
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    
    // 添加到容器
    container.appendChild(canvas);
    window.canvasCreated = true;
    
    console.log('Canvas元素已创建');
}

document.addEventListener('DOMContentLoaded', function() {
    // 页面加载后创建Canvas
    createCanvas();
});
</script>
<script src="{% static 'rds_manager/js/chart.js' %}"></script>
<script src="{% static 'rds_manager/js/score_config.js' %}"></script>
{% endblock %} 