from urllib import response
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.db.models import Q, Count, Avg, <PERSON>, Min, Sum
from django.utils import timezone
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.contrib import messages
from django.core.paginator import Paginator
import json
import logging
from datetime import datetime, timedelta
from django.db.models.functions import TruncDate
import requests
from django.contrib.auth import login
import math

from .models import (
    RDSInstance, Database, DBAccount, DBAccountPrivilege, 
    OperationLog, InstanceAccessPermission, AliCloudRegion, SlowQueryLog, SlowQueryAnalysis, SlowQueryScoreConfig
)
from .services import AliCloudRDSService, InstanceSyncService, SlowQueryService
from accounts.models import User
from utils.logger import log_execution_time

# 获取当前应用的logger
logger = logging.getLogger('rds_manager')

@login_required
def dashboard(request):
    """仪表盘视图"""
    # 统计数据
    instance_count = RDSInstance.objects.count()
    database_count = Database.objects.count()
    account_count = DBAccount.objects.count()
    log_count = OperationLog.objects.count()
    
    # 状态统计
    status_counts = RDSInstance.objects.values('status').annotate(count=Count('status'))
    status_data = {
        'labels': [status['status'] for status in status_counts],
        'data': [status['count'] for status in status_counts]
    }
    
    # 引擎统计
    engine_counts = RDSInstance.objects.values('engine').annotate(count=Count('engine'))
    engine_data = {
        'labels': [engine['engine'] for engine in engine_counts],
        'data': [engine['count'] for engine in engine_counts]
    }
    
    # 最近操作日志
    recent_logs = OperationLog.objects.all().order_by('-operation_time')[:10]
    
    context = {
        'instance_count': instance_count,
        'database_count': database_count,
        'account_count': account_count,
        'log_count': log_count,
        'status_data': json.dumps(status_data),
        'engine_data': json.dumps(engine_data),
        'recent_logs': recent_logs,
        'last_update': timezone.now()
    }
    
    return render(request, 'rds_manager/dashboard.html', context)

@login_required
def instance_list(request):
    """实例列表视图"""
    search = request.GET.get('search', '')
    region = request.GET.get('region', '')
    engine = request.GET.get('engine', '')
    status = request.GET.get('status', '')
    
    instances = RDSInstance.objects.all()
    
    # 应用过滤条件
    if search:
        instances = instances.filter(
            instance_id__icontains=search) | instances.filter(
            instance_name__icontains=search
        )
    
    if region:
        region_obj = AliCloudRegion.objects.filter(region_id=region).first()
        if region_obj:
            instances = instances.filter(region_id=region_obj.id)
    
    if engine:
        instances = instances.filter(engine=engine)
    
    if status:
        instances = instances.filter(status=status)
    
    # 获取过滤选项
    regions = AliCloudRegion.objects.all()
    engine_types = RDSInstance.objects.values_list('engine', flat=True).distinct()
    status_types = RDSInstance.objects.values_list('status', flat=True).distinct()
    
    context = {
        'instances': instances,
        'regions': regions,
        'engine_types': engine_types,
        'status_types': status_types
    }
    
    return render(request, 'rds_manager/instance_list.html', context)

@login_required
def instance_detail(request, instance_id):
    """RDS实例详情视图"""
    instance = get_object_or_404(RDSInstance, id=instance_id)
    
    # 获取该实例下的所有数据库
    databases = Database.objects.filter(instance_id=instance.instance_id)
    
    # 获取该实例下的所有账号
    accounts = DBAccount.objects.filter(instance_id=instance.instance_id)
    
    # 获取慢查询统计
    slow_query_stats = SlowQueryLog.objects.filter(
        instance_id=instance.instance_id
    ).values('database_name').annotate(
        count=Count('id'),
        avg_time=Avg('max_exe_time') / 1000.0,  # 转换为秒
        max_time=Max('max_exe_time') / 1000.0,  # 转换为秒
        min_time=Min('max_exe_time') / 1000.0,  # 转换为秒
        avg_rows=Avg('parse_row_count'),
        high_risk=Count('id', filter=Q(risk_level=2)),
        medium_risk=Count('id', filter=Q(risk_level=1)),
        low_risk=Count('id', filter=Q(risk_level=0))
    ).order_by('-count')
    
    # 获取近期的操作日志
    recent_logs = OperationLog.objects.filter(
        instance_string_id=instance.instance_id
    ).order_by('-operation_time')[:10]
    
    context = {
        'instance': instance,
        'databases': databases,
        'accounts': accounts,
        'slow_query_stats': slow_query_stats,
        'recent_logs': recent_logs
    }
    
    return render(request, 'rds_manager/instance_detail.html', context)

@login_required
@log_execution_time
def sync_instances(request):
    """同步RDS实例"""
    logger.debug(f"请求详情: {request}")
    logger.debug(f"请求方法: {request.method}")
    logger.debug(f"用户: {request.user.username} (ID: {request.user.id})")
    logger.debug(f"用户权限: is_superuser={request.user.is_superuser}, role={request.user.role}")
    
    if request.method == 'POST':
        try:
            result = InstanceSyncService.sync_all_instances(user=request.user)
            if 'error' in result:
                logger.error(f"同步实例失败: {result['error']}")
                return JsonResponse({'success': False, 'message': result['error']})
            
            message = f"同步完成。共发现{result['total']}个实例，新增{result['new']}个，更新{result['updated']}个，失败{result['failed']}个。"
            logger.info(message)
            return JsonResponse({'success': True, 'message': message})
        except Exception as e:
            logger.error(f"同步实例失败: {str(e)}")
            return JsonResponse({'success': False, 'message': f"同步失败: {str(e)}"})
    
    return JsonResponse({'success': False, 'message': '仅支持POST请求'})

@login_required
@log_execution_time
def sync_instance_databases(request, instance_id):
    """同步指定实例的数据库"""
    logger.debug(f"请求详情: {request}")
    logger.debug(f"请求方法: {request.method}")
    logger.debug(f"实例ID: {instance_id}")
    logger.debug(f"用户: {request.user.username} (ID: {request.user.id})")
    
    if request.method == 'POST':
        try:
            instance = get_object_or_404(RDSInstance, id=instance_id)
            
            # 记录用户权限信息
            logger.info(f"用户 {request.user.username} (ID: {request.user.id}) 尝试同步实例 {instance.instance_id}")
            logger.info(f"用户权限: is_superuser={request.user.is_superuser}, role={request.user.role}")
            
            # 检查权限
            has_permission = False
            if request.user.is_superuser or request.user.role == 'admin':
                has_permission = True
                logger.info("用户是超级管理员或管理员，允许访问")
            else:
                try:
                    permission = InstanceAccessPermission.objects.get(
                        user_id=request.user.id, 
                        instance_string_id=instance.instance_id
                    )
                    has_permission = True
                    logger.info(f"用户有实例访问权限: {permission.id}")
                except InstanceAccessPermission.DoesNotExist:
                    logger.warning(f"用户没有实例访问权限")
            
            if not has_permission:
                logger.warning(f"权限检查失败，拒绝访问")
                return JsonResponse({'success': False, 'message': '仅持有权限的用户可以访问'})
            
            # 使用实例的 instance_id 字符串而不是数据库 ID
            logger.debug(f"调用同步服务，实例ID: {instance.instance_id}")
            result = InstanceSyncService.sync_instance_databases(instance.instance_id, user=request.user)
            logger.debug(f"同步结果: {result}")
            
            if not result['success']:
                logger.error(f"同步失败: {result['error']}")
                return JsonResponse({'success': False, 'message': result['error']})
            
            messages.success(request, result['message'])
            logger.info(f"同步成功: {result['message']}")
            return JsonResponse({'success': True, 'message': result['message']})
        except Exception as e:
            logger.error(f"同步数据库失败: {str(e)}")
            return JsonResponse({'success': False, 'message': f"同步失败: {str(e)}"})
    
    return JsonResponse({'success': False, 'message': '仅支持POST请求'})

@login_required
def database_list(request, instance_id):
    """数据库列表视图"""
    instance = get_object_or_404(RDSInstance, id=instance_id)
    
    # 获取当前实例的数据库
    databases = Database.objects.filter(instance_id=instance.instance_id)
    
    # 为每个数据库添加慢查询统计
    for db in databases:
        db.slow_query_count = SlowQueryLog.objects.filter(
            instance_id=instance.instance_id, 
            database_name=db.name
        ).count()
    
    # 分页处理
    page = request.GET.get('page', 1)
    paginator = Paginator(databases, 10)  # 每页10条数据库记录
    
    try:
        databases_page = paginator.page(page)
    except:
        databases_page = paginator.page(1)
    
    # 按数据库名称分组统计慢查询数量
    slow_query_stats = SlowQueryLog.objects.filter(instance_id=instance.instance_id) \
                                        .values('database_name') \
                                        .annotate(count=Count('id')) \
                                        .order_by('-count')[:5]  # 获取前5个数据库
    
    context = {
        'instance': instance,
        'databases': databases_page,
        'slow_query_stats': slow_query_stats,
        'is_paginated': paginator.num_pages > 1,
        'page_obj': databases_page
    }
    
    return render(request, 'rds_manager/database_list.html', context)

@login_required
def database_detail(request, database_id):
    """数据库详情视图"""
    database = get_object_or_404(Database, id=database_id)
    
    # 查找对应的实例
    try:
        instance = RDSInstance.objects.get(instance_id=database.instance_id)
    except RDSInstance.DoesNotExist:
        instance = None
    
    # 获取这个数据库的账号权限
    privileges = DBAccountPrivilege.objects.filter(
        instance_id=database.instance_id,
        database_name=database.name
    )
    
    # 获取慢查询记录，并进行分页
    slow_queries = SlowQueryLog.objects.filter(
        instance_id=database.instance_id,
        database_name=database.name
    ).order_by('-timezone')
    
    # 分页处理
    page = request.GET.get('page', 1)
    paginator = Paginator(slow_queries, 10)  # 每页10条慢查询记录
    
    try:
        slow_queries_page = paginator.page(page)
    except:
        slow_queries_page = paginator.page(1)
    
    # 汇总统计
    total_slow_queries = slow_queries.count()
    avg_query_time = slow_queries.aggregate(avg_time=Avg('max_exe_time'))['avg_time'] or 0
    max_query_time = slow_queries.order_by('-max_exe_time').first().max_exe_time if total_slow_queries > 0 else 0
    avg_rows_examined = slow_queries.aggregate(avg_rows=Avg('parse_row_count'))['avg_rows'] or 0
    
    context = {
        'database': database,
        'instance': instance,
        'privileges': privileges,
        'slow_queries': slow_queries_page,
        'is_paginated': paginator.num_pages > 1,
        'page_obj': slow_queries_page,
        'total_slow_queries': total_slow_queries,
        'avg_query_time': avg_query_time / 1000.0,  # 转换为秒
        'max_query_time': max_query_time / 1000.0,  # 转换为秒
        'avg_rows_examined': avg_rows_examined
    }
    
    return render(request, 'rds_manager/database_detail.html', context)

@login_required
def account_list(request, instance_id):
    """账号列表视图"""
    instance = get_object_or_404(RDSInstance, id=instance_id)
    
    # 获取当前实例的账号
    accounts = DBAccount.objects.filter(instance_id=instance.instance_id)
    
    # 为每个账号添加权限信息
    for account in accounts:
        account.privileges = DBAccountPrivilege.objects.filter(
            instance_id=instance.instance_id,
            account_name=account.account_name
        )
    
    context = {
        'instance': instance,
        'accounts': accounts
    }
    
    return render(request, 'rds_manager/account_list.html', context)

@login_required
def log_list(request):
    """操作日志列表视图"""
    search = request.GET.get('search', '')
    instance_id = request.GET.get('instance_id', '')
    operation_type = request.GET.get('operation_type', '')
    date_range = request.GET.get('date_range', '')
    
    logs = OperationLog.objects.all().order_by('-operation_time')
    
    # 应用过滤条件
    if search:
        logs = logs.filter(
            operation_type__icontains=search) | logs.filter(
            operation_detail__icontains=search
        )
    
    if instance_id:
        logs = logs.filter(instance_string_id=instance_id)
    
    if operation_type:
        logs = logs.filter(operation_type=operation_type)
    
    # 日期范围过滤
    now = timezone.now()
    if date_range == 'today':
        logs = logs.filter(operation_time__date=now.date())
    elif date_range == 'yesterday':
        yesterday = now - timedelta(days=1)
        logs = logs.filter(operation_time__date=yesterday.date())
    elif date_range == 'last7days':
        seven_days_ago = now - timedelta(days=7)
        logs = logs.filter(operation_time__gte=seven_days_ago)
    elif date_range == 'last30days':
        thirty_days_ago = now - timedelta(days=30)
        logs = logs.filter(operation_time__gte=thirty_days_ago)
    
    # 分页
    paginator = Paginator(logs, 20)  # 每页显示20条
    page = request.GET.get('page')
    logs = paginator.get_page(page)
    
    # 获取操作类型选项
    operation_types = OperationLog.objects.values_list('operation_type', flat=True).distinct()
    
    context = {
        'logs': logs,
        'operation_types': operation_types
    }
    
    return render(request, 'rds_manager/log_list.html', context)

@login_required
def ajax_instance_databases(request, instance_id):
    """AJAX加载实例数据库列表"""
    instance = get_object_or_404(RDSInstance, id=instance_id)
    
    # 获取该实例的数据库
    databases = Database.objects.filter(instance_id=instance.instance_id)
    
    # 处理数据库分页
    page = request.GET.get('page', 1)
    paginator = Paginator(databases, 10)  # 每页10条记录
    
    try:
        databases_page = paginator.page(page)
    except:
        databases_page = paginator.page(1)
    
    context = {
        'instance': instance,
        'databases': databases_page,
        'total_databases': databases.count(),
    }
    
    return render(request, 'rds_manager/partials/instance_databases.html', context)

@login_required
def ajax_instance_accounts(request, instance_id):
    """AJAX加载实例账号列表"""
    instance = get_object_or_404(RDSInstance, id=instance_id)
    
    # 获取该实例的账号
    accounts = DBAccount.objects.filter(instance_id=instance.instance_id)
    
    # 处理账号分页
    page = request.GET.get('page', 1)
    paginator = Paginator(accounts, 10)  # 每页10条记录
    
    try:
        accounts_page = paginator.page(page)
    except:
        accounts_page = paginator.page(1)
    
    context = {
        'instance': instance,
        'accounts': accounts_page,
        'total_accounts': accounts.count(),
    }
    
    return render(request, 'rds_manager/partials/instance_accounts.html', context)

# API视图
class InstanceAPIView(APIView):
    """
    RDS实例API
    GET: 获取RDS实例列表
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # 获取查询参数
        search_query = request.query_params.get('q', '')
        region_id = request.query_params.get('region', '')
        engine = request.query_params.get('engine', '')
        status = request.query_params.get('status', '')
        
        # 基本查询集
        instances = RDSInstance.objects.all()
        
        # 应用过滤条件
        if search_query:
            instances = instances.filter(
                Q(instance_id__icontains=search_query) |
                Q(instance_name__icontains=search_query) |
                Q(connection_string__icontains=search_query)
            )
        
        if region_id:
            region_obj = AliCloudRegion.objects.filter(region_id=region_id).first()
            if region_obj:
                instances = instances.filter(region_id=region_obj.id)
        
        if engine:
            instances = instances.filter(engine=engine)
        
        if status:
            instances = instances.filter(status=status)
        
        # 非管理员只能查看有权限的实例
        if not request.user.is_superuser and request.user.role != 'admin':
            user_permissions = InstanceAccessPermission.objects.filter(user_id=request.user.id)
            instance_ids = [perm.instance_string_id for perm in user_permissions]
            instances = instances.filter(instance_id__in=instance_ids)
        
        # 序列化数据
        data = []
        for instance in instances:
            region = instance.region
            data.append({
                'id': instance.id,
                'instance_id': instance.instance_id,
                'instance_name': instance.instance_name,
                'engine': instance.engine,
                'engine_version': instance.engine_version,
                'region': region.region_id if region else '',
                'status': instance.status,
                'status_display': dict(RDSInstance.STATUS_CHOICES).get(instance.status, instance.status),
                'connection_string': instance.connection_string,
                'port': instance.port,
                'create_time': instance.create_time.isoformat() if instance.create_time else None,
                'expire_time': instance.expire_time.isoformat() if instance.expire_time else None,
            })
        
        return Response(data, status=status.HTTP_200_OK)


class DatabaseAPIView(APIView):
    """
    数据库API
    GET: 获取指定实例的数据库列表
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, instance_id):
        instance = get_object_or_404(RDSInstance, instance_id=instance_id)
        
        # 记录用户权限信息
        logger.info(f"用户 {request.user.username} (ID: {request.user.id}) 尝试访问实例 {instance.instance_id}")
        logger.info(f"用户权限: is_superuser={request.user.is_superuser}, role={request.user.role}")
        
        # 检查权限
        has_permission = False
        if request.user.is_superuser or request.user.role == 'admin':
            has_permission = True
            logger.info("用户是超级管理员或管理员，允许访问")
        else:
            try:
                permission = InstanceAccessPermission.objects.get(
                    user_id=request.user.id, 
                    instance_string_id=instance.instance_id
                )
                has_permission = True
                logger.info(f"用户有实例访问权限: {permission.id}")
            except InstanceAccessPermission.DoesNotExist:
                logger.warning(f"用户没有实例访问权限")
        
        if not has_permission:
            return Response({'error': '无权访问该实例'}, status=status.HTTP_403_FORBIDDEN)
        
        databases = Database.objects.filter(instance_id=instance.instance_id)
        
        # 序列化数据
        data = []
        for db in databases:
            data.append({
                'id': db.id,
                'name': db.name,
                'character_set': db.character_set,
                'description': db.description,
                'created_at': db.created_at.isoformat(),
                'updated_at': db.updated_at.isoformat(),
            })
        
        return Response(data, status=status.HTTP_200_OK)


class AccountAPIView(APIView):
    """
    账号API
    GET: 获取指定实例的账号列表
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, instance_id):
        instance = get_object_or_404(RDSInstance, instance_id=instance_id)
        
        # 记录用户权限信息
        logger.info(f"用户 {request.user.username} (ID: {request.user.id}) 尝试访问实例 {instance.instance_id}")
        logger.info(f"用户权限: is_superuser={request.user.is_superuser}, role={request.user.role}")
        
        # 检查权限
        has_permission = False
        if request.user.is_superuser or request.user.role == 'admin':
            has_permission = True
            logger.info("用户是超级管理员或管理员，允许访问")
        else:
            try:
                permission = InstanceAccessPermission.objects.get(
                    user_id=request.user.id, 
                    instance_string_id=instance.instance_id
                )
                has_permission = True
                logger.info(f"用户有实例访问权限: {permission.id}")
            except InstanceAccessPermission.DoesNotExist:
                logger.warning(f"用户没有实例访问权限")
        
        if not has_permission:
            return Response({'error': '无权访问该实例'}, status=status.HTTP_403_FORBIDDEN)
        
        accounts = DBAccount.objects.filter(instance_id=instance.instance_id)
        
        # 序列化数据
        data = []
        for account in accounts:
            # 获取账号权限
            privileges = []
            for privilege in DBAccountPrivilege.objects.filter(
                instance_id=instance.instance_id,
                account_name=account.account_name
            ):
                privileges.append({
                    'database': privilege.database_name,
                    'privilege': privilege.privilege,
                    'privilege_display': dict(DBAccountPrivilege.PRIVILEGE_CHOICES).get(privilege.privilege, privilege.privilege),
                })
            
            data.append({
                'id': account.id,
                'account_name': account.account_name,
                'account_type': account.account_type,
                'account_status': account.account_status,
                'description': account.description,
                'created_at': account.created_at.isoformat(),
                'updated_at': account.updated_at.isoformat(),
                'privileges': privileges,
            })
        
        return Response(data, status=status.HTTP_200_OK)

@login_required
def slow_query_list(request):
    """慢查询列表视图"""
    # 获取查询参数
    instance_id = request.GET.get('instance_id', '')
    database = request.GET.get('database', '')
    risk_level = request.GET.get('risk_level', '')
    date_range = request.GET.get('date_range', '7')  # 默认7天
    search = request.GET.get('search', '')
    sort_by = request.GET.get('sort_by', '-max_exe_time')  # 默认按最大执行时间降序
    
    # 基础查询
    base_query = SlowQueryLog.objects.all()
    
    # 过滤条件
    if instance_id:
        base_query = base_query.filter(instance_id=instance_id)
    
    if database:
        base_query = base_query.filter(database_name=database)
    
    if risk_level:
        base_query = base_query.filter(risk_level=risk_level)
    
    # 日期范围
    if date_range:
        days = int(date_range)
        start_date = timezone.now().date() - timedelta(days=days)
        base_query = base_query.filter(timezone__gte=start_date)
    
    # 搜索
    if search:
        base_query = base_query.filter(
            Q(sql_text__icontains=search) |
            Q(database_name__icontains=search) |
            Q(instance_id__icontains=search)
        )
    
    # 按SQL hash聚合
    queries = base_query.values(
        'sql_hash',
        'instance_id',
        'database_name',
        'sql_text'
    ).annotate(
        id=Max('id'),  # 使用最新的记录ID
        total_count=Count('*'),  # 修改为 Count('*')
        total_sql_count=Sum('sql_count'),
        max_exe_time=Max('max_exe_time'),
        total_parse_rows=Sum('parse_row_count'),
        total_return_rows=Sum('return_row_count'),
        latest_date=Max('timezone'),
        risk_level=Max('risk_level')  # 使用最高风险级别
    ).order_by('-' + sort_by.lstrip('-') if sort_by else '-max_exe_time')
    
    # 获取实例列表（用于过滤）
    instances = RDSInstance.objects.all()
    
    # 获取数据库列表（用于过滤）
    databases = SlowQueryLog.objects.values_list('database_name', flat=True).distinct()
    
    # 分页
    page = request.GET.get('page', 1)
    paginator = Paginator(queries, 20)  # 每页20条记录
    
    try:
        slow_queries = paginator.page(page)
    except:
        slow_queries = paginator.page(1)
    
    # 统计信息
    total_slow_queries = queries.count()
    high_risk_count = queries.filter(risk_level=2).count()
    medium_risk_count = queries.filter(risk_level=1).count()
    low_risk_count = queries.filter(risk_level=0).count()
    
    # 按数据库分组统计
    db_stats = base_query.values('database_name').annotate(
        count=Count('id')
    ).order_by('-count')[:5]
    
    context = {
        'slow_queries': slow_queries,
        'instances': instances,
        'databases': databases,
        'total_slow_queries': total_slow_queries,
        'high_risk_count': high_risk_count,
        'medium_risk_count': medium_risk_count,
        'low_risk_count': low_risk_count,
        'db_stats': db_stats,
        'filter_instance_id': instance_id,
        'filter_database': database,
        'filter_risk_level': risk_level,
        'filter_date_range': date_range,
        'filter_search': search,
        'sort_by': sort_by
    }
    
    return render(request, 'rds_manager/slow_query_list.html', context)

@login_required
def slow_query_detail(request, query_id):
    """慢查询详情视图"""
    slow_query = get_object_or_404(SlowQueryLog, id=query_id)
    
    # 获取分析结果，如果不存在则创建
    try:
        analysis = SlowQueryAnalysis.objects.get(
            sql_hash=slow_query.sql_hash,
            instance_id=slow_query.instance_id,
            timezone=slow_query.timezone
        )
    except SlowQueryAnalysis.DoesNotExist:
        # 如果不存在分析记录，则进行分析
        analysis_result = SlowQueryService.analyze_slow_query(slow_query)
        analysis = analysis_result.get('analysis')
    
    # 获取实例信息
    try:
        instance = RDSInstance.objects.get(instance_id=slow_query.instance_id)
    except RDSInstance.DoesNotExist:
        instance = None
    
    # 查找相似的慢查询
    similar_queries = SlowQueryLog.objects.filter(
        database_name=slow_query.database_name,
        sql_hash=slow_query.sql_hash
    ).exclude(id=slow_query.id)[:5]
    
    # 获取趋势数据
    logger.info(f"获取慢查询趋势数据: sql_hash={slow_query.sql_hash}, instance_id={slow_query.instance_id}")
    
    # 直接获取所有匹配记录，避免聚合
    raw_data = SlowQueryLog.objects.filter(
        sql_hash=slow_query.sql_hash,
        instance_id=slow_query.instance_id
    ).order_by('timezone')
    
    # 记录找到的原始数据
    raw_count = raw_data.count()
    if raw_count > 0:
        logger.info(f"找到原始记录: {raw_count}条")
        for i, record in enumerate(raw_data):
            logger.info(f"记录{i+1}: id={record.id}, timezone={record.timezone}, max_exe_time={record.max_exe_time}")
    
    # 使用字典存储每天的聚合数据
    aggregated_data = {}
    for record in raw_data:
        date_str = record.timezone.strftime('%Y-%m-%d')
        if date_str not in aggregated_data:
            aggregated_data[date_str] = {
                'max_exe_time': record.max_exe_time,
                'sql_count': record.sql_count,
                'parse_row_count': record.parse_row_count,
                'return_row_count': record.return_row_count,
                'max_lock_time': record.max_lock_time  # 添加锁等待时间
            }
        else:
            # 更新最大值
            aggregated_data[date_str]['max_exe_time'] = max(aggregated_data[date_str]['max_exe_time'], record.max_exe_time)
            aggregated_data[date_str]['sql_count'] += record.sql_count
            aggregated_data[date_str]['parse_row_count'] += record.parse_row_count
            aggregated_data[date_str]['return_row_count'] += record.return_row_count
            aggregated_data[date_str]['max_lock_time'] = max(aggregated_data[date_str]['max_lock_time'], record.max_lock_time)  # 更新最大锁等待时间
    
    # 转换为有序列表
    trend_dates = []
    execution_times = []
    execution_counts = []
    scan_rows = []
    risk_scores = []
    return_rows = []  # 添加返回行数列表
    lock_times = []   # 添加锁等待时间列表
    
    # 获取排序后的日期
    sorted_dates = sorted(aggregated_data.keys())
    
    # 查询日期对应的风险评分记录
    analysis_records = {}
    date_str_list = sorted_dates  # 准备日期列表
    
    # 批量查询每个日期对应的分析记录
    if date_str_list:
        # 将字符串日期转换为日期对象进行查询
        date_objects = [datetime.strptime(date_str, '%Y-%m-%d').date() for date_str in date_str_list]
        analyses = SlowQueryAnalysis.objects.filter(
            sql_hash=slow_query.sql_hash,
            instance_id=slow_query.instance_id,
            timezone__in=date_objects
        )
        
        # 创建查找映射
        for analysis_record in analyses:
            date_key = analysis_record.timezone.strftime('%Y-%m-%d')
            analysis_records[date_key] = analysis_record
    
    # 为每个日期填充数据
    for date_str in sorted_dates:
        data = aggregated_data[date_str]
        trend_dates.append(date_str)
        execution_times.append(float(data['max_exe_time']))
        execution_counts.append(int(data['sql_count']))
        scan_rows.append(int(data['parse_row_count']))
        return_rows.append(int(data['return_row_count']))  # 添加返回行数数据
        lock_times.append(float(data['max_lock_time']))    # 添加锁等待时间数据
        
        # 如果存在预计算的风险评分，则使用它
        if date_str in analysis_records:
            risk_scores.append(analysis_records[date_str].risk_score)
        else:
            # 否则，计算该天的风险分数
            max_exe_time_score = min(data['max_exe_time'] / 1000, 100)
            scan_rows_score = min(data['parse_row_count'] / 10000, 100)
            sql_count_score = min(data['sql_count'] * 5, 100)
            
            risk_score = (max_exe_time_score * 0.6) + (scan_rows_score * 0.3) + (sql_count_score * 0.1)
            risk_scores.append(int(risk_score))
    
    # 记录最终数据
    logger.info(f"转换后的趋势数据: 日期数={len(trend_dates)}, 日期={trend_dates}")
    logger.info(f"执行时间数据: {execution_times}")
    logger.info(f"执行次数数据: {execution_counts}")
    logger.info(f"扫描行数数据: {scan_rows}")
    logger.info(f"返回行数数据: {return_rows}")  # 添加日志
    logger.info(f"锁等待时间数据: {lock_times}")  # 添加日志
    logger.info(f"风险分数数据: {risk_scores}")
    
    # 构建评分详情
    score_details = {
        'total_score': analysis.risk_score,
        'query_count_score': analysis.query_count_score,
        'query_time_score': analysis.query_time_score,
        'scan_rows_score': analysis.scan_rows_score,
        'affected_rows_score': analysis.affected_rows_score,
        'lock_time_score': analysis.lock_time_score,
    }
    
    context = {
        'slow_query': slow_query,
        'analysis': analysis,
        'instance': instance,
        'similar_queries': similar_queries,
        'score_details': score_details,
        'trend_dates': json.dumps(trend_dates),
        'execution_times': json.dumps(execution_times),
        'execution_counts': json.dumps(execution_counts),
        'scan_rows': json.dumps(scan_rows),
        'risk_scores': json.dumps(risk_scores),
        'return_rows': json.dumps(return_rows),  # 添加到上下文
        'lock_times': json.dumps(lock_times)     # 添加到上下文
    }
    
    return render(request, 'rds_manager/slow_query_detail.html', context)

@login_required
def sync_slow_queries(request, instance_id):
    """同步慢查询统计信息"""
    if request.method != 'POST':
        return JsonResponse({'error': '仅支持POST请求'}, status=400)
    
    # 获取POST参数
    start_time_str = request.POST.get('start_time')
    end_time_str = request.POST.get('end_time')
    days_str = request.POST.get('days')
    
    # 优先使用days参数，其次使用start_time和end_time参数
    if days_str:
        try:
            days = int(days_str)
            end_time = timezone.now()
            start_time = end_time - timedelta(days=days)
            logger.info(f"使用days参数: {days}天, 日期范围: {start_time.date()} 到 {end_time.date()}")
        except ValueError:
            error_msg = 'days参数必须为整数'
            logger.error(error_msg)
            return JsonResponse({'success': False, 'error': error_msg}, status=400)
    # 如果没有提供days参数，尝试使用start_time和end_time参数
    elif start_time_str and end_time_str:
        # 解析日期
        try:
            start_time = datetime.strptime(start_time_str, '%Y-%m-%d')
            end_time = datetime.strptime(end_time_str, '%Y-%m-%d')
            # 将结束日期设置为当天的23:59:59
            end_time = end_time.replace(hour=23, minute=59, second=59)
        except ValueError:
            error_msg = '日期格式无效，请使用YYYY-MM-DD格式'
            logger.error(error_msg)
            return JsonResponse({'success': False, 'error': error_msg}, status=400)
    # 如果都没有提供，使用默认日期范围（最近3天）
    else:
        end_time = timezone.now()
        start_time = end_time - timedelta(days=3)  # 默认3天
        logger.info(f"未提供时间参数，使用默认时间范围：{start_time.date()} 到 {end_time.date()}")
    
    # 获取RDS实例
    try:
        instance = RDSInstance.objects.get(id=instance_id)
    except RDSInstance.DoesNotExist:
        error_msg = '实例不存在'
        logger.error(f"同步慢查询失败: {error_msg}")
        return JsonResponse({'success': False, 'error': error_msg}, status=404)
    
    # 记录同步开始信息
    logger.info(f"开始同步实例 {instance.instance_id} 的慢查询，时间范围: {start_time.date()} 到 {end_time.date()}")
    
    # 同步慢查询
    try:
        result = SlowQueryService.sync_slow_queries(
            instance_id=instance.instance_id,
            start_time=start_time,
            end_time=end_time,
            user=request.user
        )
        
        return JsonResponse(result)
    except Exception as e:
        error_msg = f'同步失败: {str(e)}'
        logger.error(f"同步慢查询失败: {error_msg}")
        return JsonResponse({'success': False, 'error': error_msg}, status=500)

@login_required
def get_sql_samples(request, query_id):
    """获取SQL样本数据的API端点"""
    slow_query = get_object_or_404(SlowQueryLog, id=query_id)
    
    # 获取实例信息
    try:
        instance = RDSInstance.objects.get(instance_id=slow_query.instance_id)
    except RDSInstance.DoesNotExist:
        instance = None
    
    # 获取SQL样本
    sql_samples = []
    try:
        if instance:
            # 创建RDS服务
            rds_service = AliCloudRDSService(region_id=instance.region.region_id if instance.region else None)
            
            # 记录调试信息
            logger.info(f"开始获取SQL样本: instance_id={instance.instance_id}, region_id={instance.region.region_id if instance.region else 'None'}")
            logger.info(f"SQL哈希: {slow_query.sql_hash}, 数据库: {slow_query.database_name}")
            
            # 获取慢查询的统计日期
            if hasattr(slow_query.timezone, 'date'):
                query_date = slow_query.timezone.date()  # 如果timezone是datetime对象
            else:
                query_date = slow_query.timezone  # 已经是date对象
            
            # 准备时间范围（只查询特定的一天）
            start_time = datetime.combine(query_date, datetime.min.time())
            start_time = timezone.make_aware(start_time)  # 转换为带时区的datetime
            
            end_time = datetime.combine(query_date, datetime.max.time())
            end_time = timezone.make_aware(end_time)  # 转换为带时区的datetime
            
            # 转换为阿里云API需要的时间格式 yyyy-MM-ddTHH:mmZ（注意这里的格式）
            start_time_str = start_time.astimezone(timezone.utc).strftime("%Y-%m-%dT%H:%MZ")
            end_time_str = end_time.astimezone(timezone.utc).strftime("%Y-%m-%dT%H:%MZ")
            
            logger.info(f"查询指定日期: {query_date}")
            logger.info(f"API查询时间范围: {start_time_str} 到 {end_time_str}")
            
            # 获取原始SQL文本用于模糊匹配
            sql_text = slow_query.sql_text or ""
            sql_text_normalized = ' '.join(sql_text.split()).lower()
            logger.info(f"原始SQL文本: {sql_text_normalized[:100]}...")
            
            # 获取SQL样本记录（使用慢查询统计里的数据库名和SQL哈希）
            records = rds_service.get_slow_query_records(
                instance_id=slow_query.instance_id,
                start_time=start_time_str,
                end_time=end_time_str,
                db_name=slow_query.database_name,
                sql_hash=slow_query.sql_hash,  # 直接在API中使用SQL哈希
            )
            
            logger.info(f"获取到的SQL样本数量: {len(records)}")
            
            # 最终返回的样本
            result_records = []
            
            # 统计匹配上的记录数
            match_count = 0
            
            # 只保留SQL匹配的样本（由于我们已通过API直接查询了指定SQL哈希，这里主要是二次确认）
            for record in records:
                if record.get('SQLHash') == slow_query.sql_hash:
                    result_records.append(record)
                    match_count += 1
            
            logger.info(f"匹配到的SQL样本数量: {match_count}")
            
            # 按照执行时间排序并且最多返回5条
            result_records = sorted(result_records, key=lambda x: x.get('QueryTimes', 0), reverse=True)[:5]
            
            logger.info(f"最终显示的SQL样本数量: {len(result_records)}")
            
            # 将筛选后的记录转换为样本数据
            for match in result_records:
                item = match
                execution_time = datetime.fromtimestamp(float(item.get('QueryTimes', 0)) / 1000, timezone.utc)
                execution_time_display = execution_time.strftime("%Y-%m-%d %H:%M:%S")
                
                sample = {
                    'sql_text': item.get('SQLText', ''),
                    'execution_time': execution_time_display,
                    'query_time': float(item.get('QueryTimes', 0)) * 1000,  # 转换为毫秒
                    'lock_time': float(item.get('LockTimes', 0)) * 1000,    # 转换为毫秒
                    'rows_examined': item.get('ParseRowCounts', 0),
                    'host_address': item.get('HostAddress', 'N/A'),  # 添加主机地址信息
                    'match_type': 'SQL匹配'
                }
                sql_samples.append(sample)
                logger.info(f"添加样本: 执行时间={execution_time_display}")
        else:
            logger.warning("API返回的响应中没有Items.SQLSlowRecord字段")
            if 'Code' in response:
                logger.warning(f"API返回错误代码: {response.get('Code')}, 消息: {response.get('Message', '')}")
    except Exception as e:
        logger.error(f"获取SQL样本出错: {str(e)}", exc_info=True)
    
    # 返回JSON响应
    return JsonResponse({
        'success': True,
        'samples': sql_samples
    })

@login_required
def score_config_list(request):
    """慢查询评分配置列表视图"""
    # 获取所有配置
    configs = SlowQueryScoreConfig.objects.all().order_by('-is_active', '-created_at')
    
    # 获取当前激活的配置（默认显示慢查询配置）
    try:
        active_config = SlowQueryScoreConfig.get_active_config(config_type='slowlog')
    except Exception:
        active_config = None
    
    context = {
        'configs': configs,
        'active_config': active_config
    }
    
    return render(request, 'rds_manager/score_config_list.html', context)

@login_required
def score_config_detail(request, config_id=None):
    """慢查询评分配置详情视图"""
    # 编辑现有配置或创建新配置
    if config_id:
        config = get_object_or_404(SlowQueryScoreConfig, id=config_id)
        is_new = False
    else:
        config = None
        is_new = True
    
    if request.method == 'POST':
        # 处理表单提交
        try:
            # 获取基本信息
            name = request.POST.get('name')
            description = request.POST.get('description', '')
            is_active = request.POST.get('is_active') == 'on'
            
            # 查询次数参数
            query_count_min = int(request.POST.get('query_count_min', 5))
            query_count_max = int(request.POST.get('query_count_max', 500))
            query_count_weight = float(request.POST.get('query_count_weight', 0.4))
            query_count_curve = request.POST.get('query_count_curve', 'linear')
            
            # 查询时间参数
            query_time_min = int(request.POST.get('query_time_min', 0))
            query_time_max = int(request.POST.get('query_time_max', 10000))
            query_time_weight = float(request.POST.get('query_time_weight', 0.3))
            query_time_curve = request.POST.get('query_time_curve', 'sine')
            
            # 扫描行数参数
            scan_rows_min = int(request.POST.get('scan_rows_min', 1))
            scan_rows_max = int(request.POST.get('scan_rows_max', 1000000))
            scan_rows_weight = float(request.POST.get('scan_rows_weight', 0.2))
            scan_rows_curve = request.POST.get('scan_rows_curve', 'polynomial')
            
            # 影响行数参数
            affected_rows_min = int(request.POST.get('affected_rows_min', 0))
            affected_rows_max = int(request.POST.get('affected_rows_max', 500))
            affected_rows_weight = float(request.POST.get('affected_rows_weight', 0.05))
            affected_rows_curve = request.POST.get('affected_rows_curve', 'linear')
            
            # 锁等待时间参数
            lock_time_min = int(request.POST.get('lock_time_min', 0))
            lock_time_max = int(request.POST.get('lock_time_max', 1000))
            lock_time_weight = float(request.POST.get('lock_time_weight', 0.05))
            lock_time_curve = request.POST.get('lock_time_curve', 'exponential')
            
            # 多项式系数
            polynomial_a = float(request.POST.get('polynomial_a', 0.0))
            polynomial_b = float(request.POST.get('polynomial_b', 0.02))
            polynomial_c = float(request.POST.get('polynomial_c', 0.001))
            polynomial_d = float(request.POST.get('polynomial_d', 0.0005))
            polynomial_e = float(request.POST.get('polynomial_e', 0.00001))
            polynomial_f = float(request.POST.get('polynomial_f', 0.000001))
            
            # 验证权重总和是否等于1
            weights_sum = query_count_weight + query_time_weight + scan_rows_weight + affected_rows_weight + lock_time_weight
            if not 0.99 <= weights_sum <= 1.01:
                messages.error(request, f"权重总和必须等于1，当前总和: {weights_sum:.2f}")
                raise ValueError("权重总和必须等于1")
            
            # 获取配置类型
            config_type = request.POST.get('config_type', 'slowlog')

            # 如果是激活配置，将同类型的其他配置设为非激活
            if is_active:
                SlowQueryScoreConfig.objects.filter(
                    config_type=config_type,
                    is_active=True
                ).update(is_active=False)
            
            # 创建或更新配置
            if is_new:
                config = SlowQueryScoreConfig.objects.create(
                    name=name,
                    config_type=config_type,
                    description=description,
                    is_active=is_active,
                    query_count_min=query_count_min,
                    query_count_max=query_count_max,
                    query_count_weight=query_count_weight,
                    query_count_curve=query_count_curve,
                    query_time_min=query_time_min,
                    query_time_max=query_time_max,
                    query_time_weight=query_time_weight,
                    query_time_curve=query_time_curve,
                    scan_rows_min=scan_rows_min,
                    scan_rows_max=scan_rows_max,
                    scan_rows_weight=scan_rows_weight,
                    scan_rows_curve=scan_rows_curve,
                    affected_rows_min=affected_rows_min,
                    affected_rows_max=affected_rows_max,
                    affected_rows_weight=affected_rows_weight,
                    affected_rows_curve=affected_rows_curve,
                    lock_time_min=lock_time_min,
                    lock_time_max=lock_time_max,
                    lock_time_weight=lock_time_weight,
                    lock_time_curve=lock_time_curve,
                    polynomial_a=polynomial_a,
                    polynomial_b=polynomial_b,
                    polynomial_c=polynomial_c,
                    polynomial_d=polynomial_d,
                    polynomial_e=polynomial_e,
                    polynomial_f=polynomial_f
                )
                messages.success(request, f"成功创建配置: {name}")
            else:
                # 更新现有配置
                config.name = name
                config.config_type = config_type
                config.description = description
                config.is_active = is_active
                config.query_count_min = query_count_min
                config.query_count_max = query_count_max
                config.query_count_weight = query_count_weight
                config.query_count_curve = query_count_curve
                config.query_time_min = query_time_min
                config.query_time_max = query_time_max
                config.query_time_weight = query_time_weight
                config.query_time_curve = query_time_curve
                config.scan_rows_min = scan_rows_min
                config.scan_rows_max = scan_rows_max
                config.scan_rows_weight = scan_rows_weight
                config.scan_rows_curve = scan_rows_curve
                config.affected_rows_min = affected_rows_min
                config.affected_rows_max = affected_rows_max
                config.affected_rows_weight = affected_rows_weight
                config.affected_rows_curve = affected_rows_curve
                config.lock_time_min = lock_time_min
                config.lock_time_max = lock_time_max
                config.lock_time_weight = lock_time_weight
                config.lock_time_curve = lock_time_curve
                config.polynomial_a = polynomial_a
                config.polynomial_b = polynomial_b
                config.polynomial_c = polynomial_c
                config.polynomial_d = polynomial_d
                config.polynomial_e = polynomial_e
                config.polynomial_f = polynomial_f
                config.save()
                messages.success(request, f"成功更新配置: {name}")
            
            # 重定向到列表页
            return redirect('rds_manager:score_config_list')
        
        except (ValueError, TypeError) as e:
            messages.error(request, f"表单数据错误: {str(e)}")
        except Exception as e:
            messages.error(request, f"保存配置失败: {str(e)}")
    
    # 如果是GET请求或处理失败的POST请求，显示表单
    context = {
        'config': config,
        'is_new': is_new,
        'curve_choices': SlowQueryScoreConfig.CURVE_CHOICES
    }
    
    return render(request, 'rds_manager/score_config_form.html', context)

@login_required
def score_config_activate(request, config_id):
    """激活指定的慢查询评分配置"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '仅支持POST请求'}, status=400)
    
    config = get_object_or_404(SlowQueryScoreConfig, id=config_id)
    
    try:
        # 将同类型的其他配置设为非激活
        SlowQueryScoreConfig.objects.filter(
            config_type=config.config_type,
            is_active=True
        ).update(is_active=False)

        # 激活当前配置
        config.is_active = True
        config.save()
        
        messages.success(request, f"已激活配置: {config.name}")
        return JsonResponse({'success': True})
    except Exception as e:
        messages.error(request, f"激活配置失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
def score_config_delete(request, config_id):
    """删除指定的慢查询评分配置"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '仅支持POST请求'}, status=400)
    
    config = get_object_or_404(SlowQueryScoreConfig, id=config_id)
    
    try:
        # 不允许删除激活的配置
        if config.is_active:
            messages.error(request, "不能删除激活的配置，请先激活其他配置")
            return JsonResponse({'success': False, 'error': '不能删除激活的配置'}, status=400)
        
        # 删除配置
        config_name = config.name
        config.delete()
        
        messages.success(request, f"已删除配置: {config_name}")
        return JsonResponse({'success': True})
    except Exception as e:
        messages.error(request, f"删除配置失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
def score_config_test(request, config_id=None):
    """测试慢查询评分配置"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '仅支持POST请求'}, status=400)
    
    try:
        # 获取配置
        if config_id:
            config = get_object_or_404(SlowQueryScoreConfig, id=config_id)
        else:
            # 如果没有指定配置ID，使用表单中的数据创建临时配置
            config = SlowQueryScoreConfig(
                name="临时测试配置",
                config_type=request.POST.get('config_type', 'slowlog'),
                query_count_min=int(request.POST.get('query_count_min', 5)),
                query_count_max=int(request.POST.get('query_count_max', 500)),
                query_count_weight=float(request.POST.get('query_count_weight', 0.4)),
                query_count_curve=request.POST.get('query_count_curve', 'linear'),
                query_time_min=int(request.POST.get('query_time_min', 0)),
                query_time_max=int(request.POST.get('query_time_max', 10000)),
                query_time_weight=float(request.POST.get('query_time_weight', 0.3)),
                query_time_curve=request.POST.get('query_time_curve', 'sine'),
                scan_rows_min=int(request.POST.get('scan_rows_min', 1)),
                scan_rows_max=int(request.POST.get('scan_rows_max', 1000000)),
                scan_rows_weight=float(request.POST.get('scan_rows_weight', 0.2)),
                scan_rows_curve=request.POST.get('scan_rows_curve', 'polynomial'),
                affected_rows_min=int(request.POST.get('affected_rows_min', 0)),
                affected_rows_max=int(request.POST.get('affected_rows_max', 500)),
                affected_rows_weight=float(request.POST.get('affected_rows_weight', 0.05)),
                affected_rows_curve=request.POST.get('affected_rows_curve', 'linear'),
                lock_time_min=int(request.POST.get('lock_time_min', 0)),
                lock_time_max=int(request.POST.get('lock_time_max', 1000)),
                lock_time_weight=float(request.POST.get('lock_time_weight', 0.05)),
                lock_time_curve=request.POST.get('lock_time_curve', 'exponential'),
                polynomial_a=float(request.POST.get('polynomial_a', 0.0)),
                polynomial_b=float(request.POST.get('polynomial_b', 0.02)),
                polynomial_c=float(request.POST.get('polynomial_c', 0.001)),
                polynomial_d=float(request.POST.get('polynomial_d', 0.0005)),
                polynomial_e=float(request.POST.get('polynomial_e', 0.00001)),
                polynomial_f=float(request.POST.get('polynomial_f', 0.000001))
            )
        
        # 从请求中获取测试数据
        query_count = int(request.POST.get('test_query_count', 50))
        query_time = int(request.POST.get('test_query_time', 1000))
        scan_rows = int(request.POST.get('test_scan_rows', 10000))
        affected_rows = int(request.POST.get('test_affected_rows', 100))
        lock_time = int(request.POST.get('test_lock_time', 100))
        
        # 计算各项得分
        def calculate_score(value, min_val, max_val, curve_type, config):
            # 超出范围处理
            if value <= min_val:
                return 0
            if value >= max_val:
                return 1
                
            # 标准化到0-1区间
            normalized = (value - min_val) / (max_val - min_val)
            
            # 根据曲线类型计算
            if curve_type == 'linear':
                # 线性曲线: Y = X
                return normalized
            elif curve_type == 'exponential':
                # 指数曲线: Y = 2^(X * log₂(100)) / 100
                return pow(2, normalized * math.log2(100)) / 100
            elif curve_type == 'logarithmic':
                # 对数曲线: Y = log₁₀(9*X+1)
                return math.log10(9 * normalized + 1)
            elif curve_type == 'sine':
                # 正弦曲线: Y = sin(X * π/2)
                return math.sin(normalized * math.pi / 2)
            elif curve_type == 'polynomial':
                # 多项式曲线: Y = a + b·X + c·X² + d·X³ + e·X⁴ + f·X⁵
                a, b, c, d, e, f = config.polynomial_a, config.polynomial_b, config.polynomial_c, config.polynomial_d, config.polynomial_e, config.polynomial_f
                
                # 这里将范围缩小到0-20，适合多项式计算
                x = normalized * 20
                return min(1.0, a + b*x + c*x**2 + d*x**3 + e*x**4 + f*x**5)
            else:
                # 默认使用线性曲线
                return normalized
        
        # 计算各项得分
        query_count_score = calculate_score(query_count, config.query_count_min, config.query_count_max, config.query_count_curve, config)
        query_time_score = calculate_score(query_time, config.query_time_min, config.query_time_max, config.query_time_curve, config)
        scan_rows_score = calculate_score(scan_rows, config.scan_rows_min, config.scan_rows_max, config.scan_rows_curve, config)
        affected_rows_score = calculate_score(affected_rows, config.affected_rows_min, config.affected_rows_max, config.affected_rows_curve, config)
        lock_time_score = calculate_score(lock_time, config.lock_time_min, config.lock_time_max, config.lock_time_curve, config)
        
        # 计算总分
        total_score = (
            query_count_score * config.query_count_weight +
            query_time_score * config.query_time_weight +
            scan_rows_score * config.scan_rows_weight +
            affected_rows_score * config.affected_rows_weight +
            lock_time_score * config.lock_time_weight
        )
        
        # 总分(0-1)换算为风险级别(0-100)
        risk_score = int(total_score * 100)
        
        # 设置风险等级
        if risk_score >= 70:
            risk_level = "高风险"
        elif risk_score >= 30:
            risk_level = "中风险"
        else:
            risk_level = "低风险"
        
        # 返回测试结果
        result = {
            'success': True,
            'query_count_score': round(query_count_score * 100),
            'query_time_score': round(query_time_score * 100),
            'scan_rows_score': round(scan_rows_score * 100),
            'affected_rows_score': round(affected_rows_score * 100),
            'lock_time_score': round(lock_time_score * 100),
            'total_score': round(total_score * 100),
            'risk_level': risk_level
        }
        
        # 生成曲线数据点
        curves = {}
        for metric, curve_type in [
            ('query_count', config.query_count_curve),
            ('query_time', config.query_time_curve),
            ('scan_rows', config.scan_rows_curve),
            ('affected_rows', config.affected_rows_curve),
            ('lock_time', config.lock_time_curve)
        ]:
            # 获取相关参数
            if metric == 'query_count':
                min_val, max_val = config.query_count_min, config.query_count_max
                current_val = query_count
            elif metric == 'query_time':
                min_val, max_val = config.query_time_min, config.query_time_max
                current_val = query_time
            elif metric == 'scan_rows':
                min_val, max_val = config.scan_rows_min, config.scan_rows_max
                current_val = scan_rows
            elif metric == 'affected_rows':
                min_val, max_val = config.affected_rows_min, config.affected_rows_max
                current_val = affected_rows
            elif metric == 'lock_time':
                min_val, max_val = config.lock_time_min, config.lock_time_max
                current_val = lock_time
            
            # 生成20个数据点
            step = (max_val - min_val) / 19  # 19个间隔，共20个点
            points = []
            for i in range(20):
                x = min_val + i * step
                y = calculate_score(x, min_val, max_val, curve_type, config) * 100
                points.append({'x': x, 'y': y})
            
            # 添加实际值的点
            current_point = {
                'x': current_val,
                'y': calculate_score(current_val, min_val, max_val, curve_type, config) * 100,
                'is_current': True
            }
            
            curves[metric] = {
                'points': points,
                'current': current_point,
                'curve_type': curve_type
            }
        
        result['curves'] = curves
        
        return JsonResponse(result)
    
    except Exception as e:
        logger.error(f"测试评分配置失败: {str(e)}", exc_info=True)
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
