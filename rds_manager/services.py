import json
import logging
from datetime import datetime
from django.utils import timezone
from django.conf import settings
from loguru import logger
from cryptography.fernet import Fernet
import time
import math
import re

# 模拟阿里云SDK
class AliCloudClient:
    def __init__(self, region_id, access_key_id, access_key_secret):
        cipher_suite = Fernet(settings.ALIYUN_SUITE_KEY.encode())
        self.region_id = region_id
        self.access_key_id = access_key_id
        self.access_key_secret = cipher_suite.decrypt(access_key_secret.encode()).decode()
        
    def request(self, action, params=None):
        # 模拟请求，实际项目中需替换为真实的SDK调用
        if action == 'DescribeDBInstances':
            # 模拟返回RDS实例列表
            return {
                'Items': {
                    'DBInstance': [
                        {
                            'DBInstanceId': 'rm-test001',
                            'DBInstanceDescription': '测试实例1',
                            'Engine': 'MySQL',
                            'EngineVersion': '8.0',
                            'DBInstanceClass': 'mysql.n2.medium.1',
                            'DBInstanceStatus': 'Running',
                            'RegionId': self.region_id,
                            'ZoneId': f'{self.region_id}-a',
                            'ConnectionString': f'rm-test001.mysql.{self.region_id}.rds.aliyuncs.com',
                            'Port': '3306',
                            'DBInstanceType': 'Primary',
                            'VpcId': 'vpc-123456',
                            'CreateTime': '2023-01-01T12:00:00Z',
                            'ExpireTime': '2024-01-01T12:00:00Z'
                        },
                        {
                            'DBInstanceId': 'rm-test002',
                            'DBInstanceDescription': '测试实例2',
                            'Engine': 'PostgreSQL',
                            'EngineVersion': '14.0',
                            'DBInstanceClass': 'pg.n2.medium.1',
                            'DBInstanceStatus': 'Running',
                            'RegionId': self.region_id,
                            'ZoneId': f'{self.region_id}-b',
                            'ConnectionString': f'rm-test002.pg.{self.region_id}.rds.aliyuncs.com',
                            'Port': '5432',
                            'DBInstanceType': 'Primary',
                            'VpcId': 'vpc-789012',
                            'CreateTime': '2023-02-01T12:00:00Z',
                            'ExpireTime': '2024-02-01T12:00:00Z'
                        }
                    ]
                },
                'TotalRecordCount': 2,
                'PageNumber': params.get('PageNumber', 1),
                'PageSize': params.get('PageSize', 10)
            }
        elif action == 'DescribeDBInstanceAttribute':
            # 模拟返回实例详情
            instance_id = params.get('DBInstanceId')
            return {
                'Items': {
                    'DBInstanceAttribute': [
                        {
                            'DBInstanceId': instance_id,
                            'DBInstanceDescription': f'测试实例 {instance_id}',
                            'Engine': 'MySQL',
                            'EngineVersion': '8.0',
                            'DBInstanceClass': 'mysql.n2.medium.1',
                            'DBInstanceStatus': 'Running',
                            'RegionId': self.region_id,
                            'ZoneId': f'{self.region_id}-a',
                            'ConnectionString': f'{instance_id}.mysql.{self.region_id}.rds.aliyuncs.com',
                            'Port': '3306',
                            'DBInstanceType': 'Primary',
                            'VpcId': 'vpc-123456',
                            'CreateTime': '2023-01-01T12:00:00Z',
                            'ExpireTime': '2024-01-01T12:00:00Z'
                        }
                    ]
                }
            }
        elif action == 'DescribeDatabases':
            # 模拟返回数据库列表
            instance_id = params.get('DBInstanceId')
            return {
                'Databases': {
                    'Database': [
                        {
                            'DBName': 'testdb1',
                            'DBInstanceId': instance_id,
                            'CharacterSetName': 'utf8mb4',
                            'DBDescription': '测试数据库1',
                            'DBStatus': 'Running'
                        },
                        {
                            'DBName': 'testdb2',
                            'DBInstanceId': instance_id,
                            'CharacterSetName': 'utf8mb4',
                            'DBDescription': '测试数据库2',
                            'DBStatus': 'Running'
                        }
                    ]
                }
            }
        elif action == 'DescribeAccounts':
            # 模拟返回账号列表
            instance_id = params.get('DBInstanceId')
            return {
                'Accounts': {
                    'DBInstanceAccount': [
                        {
                            'AccountName': 'admin',
                            'DBInstanceId': instance_id,
                            'AccountStatus': 'Available',
                            'AccountType': 'Super',
                            'AccountDescription': '超级管理员账号'
                        },
                        {
                            'AccountName': 'readonly',
                            'DBInstanceId': instance_id,
                            'AccountStatus': 'Available',
                            'AccountType': 'Normal',
                            'AccountDescription': '只读账号'
                        }
                    ]
                }
            }
        elif action == 'DescribeDBInstanceNetInfo':
            # 模拟返回网络信息
            instance_id = params.get('DBInstanceId')
            return {
                'DBInstanceNetInfos': {
                    'DBInstanceNetInfo': [
                        {
                            'ConnectionString': f'{instance_id}.mysql.{self.region_id}.rds.aliyuncs.com',
                            'IPAddress': '***********',
                            'IPType': 'Public',
                            'Port': '3306',
                            'VPCId': 'vpc-123456',
                            'VSwitchId': 'vsw-123456'
                        }
                    ]
                }
            }
        return {}


class AliCloudRDSService:
    """阿里云RDS服务，用于与阿里云API交互"""
    
    def __init__(self, region_id=None, access_key_id=None, access_key_secret=None):
        """初始化RDS服务
        
        Args:
            region_id: 地域ID，如果不指定则使用配置文件中的默认值
            access_key_id: 访问密钥ID，如果不指定则使用配置文件中的默认值
            access_key_secret: 访问密钥密钥，如果不指定则使用配置文件中的默认值
        """
        self.region_id = region_id or settings.ALIYUN_REGION_ID
        self.access_key_id = access_key_id or settings.ALIYUN_ACCESS_KEY_ID
        self.access_key_secret = access_key_secret or settings.ALIYUN_ACCESS_KEY_SECRET
        
        # 解密密钥
        try:
            cipher_suite = Fernet(settings.ALIYUN_SUITE_KEY.encode())
            self.access_key_secret = cipher_suite.decrypt(self.access_key_secret.encode()).decode()
        except Exception as e:
            logger.error(f"解密密钥失败: {str(e)}")
    
    def get_client(self, region_id=None):
        """获取阿里云客户端
        
        Args:
            region_id: 地域ID，如果不指定则使用实例初始化时的值
            
        Returns:
            阿里云客户端
        """
        from aliyunsdkcore.client import AcsClient
        from aliyunsdkcore.request import CommonRequest
        
        # 如果指定了region_id则使用指定的值，否则使用实例的值
        region = region_id or self.region_id
        
        # 创建AcsClient实例，设置较长的超时时间
        # 注意：此SDK版本不支持read_timeout参数
        client = AcsClient(
            self.access_key_id,
            self.access_key_secret,
            region,
            timeout=300,  # 连接超时时间：300秒
            connect_timeout=60  # 建立连接超时：60秒
        )
        
        return client
    
    def list_instances(self, region_id=None, page_number=1, page_size=100):
        """获取RDS实例列表
        
        Args:
            region_id: 地域ID
            page_number: 页码
            page_size: 每页数量
            
        Returns:
            RDS实例列表
        """
        from aliyunsdkrds.request.v20140815.DescribeDBInstancesRequest import DescribeDBInstancesRequest
        import json
        
        client = self.get_client(region_id)
        
        # 创建请求
        request = DescribeDBInstancesRequest()
        request.set_accept_format('json')
        request.set_PageNumber(page_number)
        request.set_PageSize(page_size)
        
        # 发送请求
        try:
            response = client.do_action_with_exception(request)
            return json.loads(response)
        except Exception as e:
            logger.error(f"获取RDS实例列表失败: {str(e)}")
            # 如果API调用失败，返回模拟数据以保证应用可用性
            return {
                'Items': {
                    'DBInstance': []
                },
                'TotalRecordCount': 0,
                'PageNumber': page_number,
                'PageSize': page_size
            }
    
    def get_instance_detail(self, instance_id, region_id=None):
        """获取RDS实例详情
        
        Args:
            instance_id: 实例ID
            region_id: 地域ID
            
        Returns:
            RDS实例详情
        """
        from aliyunsdkrds.request.v20140815.DescribeDBInstanceAttributeRequest import DescribeDBInstanceAttributeRequest
        import json
        
        client = self.get_client(region_id)
        
        # 创建请求
        request = DescribeDBInstanceAttributeRequest()
        request.set_accept_format('json')
        request.set_DBInstanceId(instance_id)
        
        # 发送请求
        try:
            response = client.do_action_with_exception(request)
            return json.loads(response)
        except Exception as e:
            logger.error(f"获取RDS实例详情失败: {str(e)}")
            # 如果API调用失败，返回空数据
            return {
                'Items': {
                    'DBInstanceAttribute': []
                }
            }
    
    def get_instance_databases(self, instance_id, region_id=None):
        """获取RDS实例的数据库列表
        
        Args:
            instance_id: 实例ID
            region_id: 地域ID
            
        Returns:
            数据库列表
        """
        from aliyunsdkrds.request.v20140815.DescribeDatabasesRequest import DescribeDatabasesRequest
        import json
        
        client = self.get_client(region_id)
        
        # 创建请求
        request = DescribeDatabasesRequest()
        request.set_accept_format('json')
        request.set_DBInstanceId(instance_id)
        
        # 发送请求
        try:
            response = client.do_action_with_exception(request)
            return json.loads(response)
        except Exception as e:
            logger.error(f"获取数据库列表失败: {str(e)}")
            # 如果API调用失败，返回空数据
            return {
                'Databases': {
                    'Database': []
                }
            }
    
    def get_instance_accounts(self, instance_id, region_id=None):
        """获取RDS实例的账号列表
        
        Args:
            instance_id: 实例ID
            region_id: 地域ID
            
        Returns:
            账号列表
        """
        from aliyunsdkrds.request.v20140815.DescribeAccountsRequest import DescribeAccountsRequest
        import json
        
        client = self.get_client(region_id)
        
        # 创建请求
        request = DescribeAccountsRequest()
        request.set_accept_format('json')
        request.set_DBInstanceId(instance_id)
        
        # 发送请求
        try:
            response = client.do_action_with_exception(request)
            return json.loads(response)
        except Exception as e:
            logger.error(f"获取账号列表失败: {str(e)}")
            # 如果API调用失败，返回空数据
            return {
                'Accounts': {
                    'DBInstanceAccount': []
                }
            }
    
    def get_instance_net_info(self, instance_id, region_id=None):
        """获取RDS实例的网络信息
        
        Args:
            instance_id: 实例ID
            region_id: 地域ID
            
        Returns:
            网络信息
        """
        from aliyunsdkrds.request.v20140815.DescribeDBInstanceNetInfoRequest import DescribeDBInstanceNetInfoRequest
        import json
        
        client = self.get_client(region_id)
        
        # 创建请求
        request = DescribeDBInstanceNetInfoRequest()
        request.set_accept_format('json')
        request.set_DBInstanceId(instance_id)
        
        # 发送请求
        try:
            response = client.do_action_with_exception(request)
            return json.loads(response)
        except Exception as e:
            logger.error(f"获取网络信息失败: {str(e)}")
            # 如果API调用失败，返回空数据
            return {
                'DBInstanceNetInfos': {
                    'DBInstanceNetInfo': []
                }
            }
    
    def get_slow_query_logs(self, instance_id, start_time, end_time, page_number=1, page_size=100, region_id=None):
        """获取RDS实例的慢查询日志统计情况
        
        Args:
            instance_id: 实例ID
            start_time: 开始时间，格式：yyyy-MM-ddZ（UTC时间）
            end_time: 结束时间，格式：yyyy-MM-ddZ（UTC时间）
            page_number: 页码
            page_size: 每页数量
            region_id: 地域ID
            
        Returns:
            慢查询日志统计列表
        """
        from aliyunsdkrds.request.v20140815.DescribeSlowLogsRequest import DescribeSlowLogsRequest
        import json
        
        client = self.get_client(region_id)
        
        # 创建请求
        request = DescribeSlowLogsRequest()
        request.set_accept_format('json')
        request.set_DBInstanceId(instance_id)
        request.set_StartTime(start_time)
        request.set_EndTime(end_time)
        request.set_PageNumber(page_number)
        request.set_PageSize(page_size)
        
        # 发送请求
        try:
            response = client.do_action_with_exception(request)
            return json.loads(response)
        except Exception as e:
            # 记录错误但不处理，让上层调用者处理限流等异常
            logger.error(f"获取慢查询日志统计失败: {str(e)}")
            # 直接抛出异常，让上层处理
            raise
    
    def get_slow_query_records(self, instance_id, start_time, end_time, db_name=None, sql_hash=None, page_number=1, page_size=10, description=None):
        """
        获取RDS实例的慢查询明细记录
        :param instance_id: 实例ID
        :param start_time: 开始时间（格式：yyyy-MM-ddTHH:mmZ）
        :param end_time: 结束时间（格式：yyyy-MM-ddTHH:mmZ）
        :param db_name: 数据库名称（可选）
        :param sql_hash: SQL哈希值（可选，直接在API请求中使用）
        :param page_number: 页码（默认为1）
        :param page_size: 每页记录数（默认为10）
        :param description: 查询描述（可选）
        :return: 慢查询明细记录列表
        """
        from aliyunsdkrds.request.v20140815.DescribeSlowLogRecordsRequest import DescribeSlowLogRecordsRequest
        from aliyunsdkcore.acs_exception.exceptions import ServerException
        import json
        import time
        
        client = self.get_client()
        request = DescribeSlowLogRecordsRequest()
        request.set_accept_format('json')
        
        request.set_DBInstanceId(instance_id)
        request.set_StartTime(start_time)
        request.set_EndTime(end_time)
        
        if db_name:
            request.set_DBName(db_name)
        
        # 如果提供了SQL哈希值，直接在API请求中使用
        if sql_hash:
            request.set_SQLHASH(sql_hash)
        
        request.set_PageNumber(page_number)
        request.set_PageSize(page_size)
        
        logger.info(f"查询慢查询明细: {instance_id}, {start_time} - {end_time}, DB: {db_name}, " +
                    f"页码: {page_number}, 每页: {page_size}, 描述: {description or '无'}")
        
        max_retries = 5
        retry_count = 0
        backoff_time = 1  # 初始等待时间（秒）
        
        while retry_count < max_retries:
            try:
                response = client.do_action_with_exception(request)
                response_dict = json.loads(response)
                
                if 'Items' in response_dict and 'SQLSlowRecord' in response_dict['Items']:
                    items = response_dict['Items']['SQLSlowRecord']
                    logger.info(f"获取到 {len(items)} 条慢查询明细记录")
                    return items
                else:
                    logger.warning(f"未找到慢查询明细记录: {response_dict}")
                    return []
                
            except ServerException as e:
                # 处理服务器端异常（如503 Service Unavailable）
                http_status = getattr(e, 'get_http_status', lambda: None)()
                if http_status == 503 or "ServiceUnavailable" in str(e):
                    retry_count += 1
                    wait_time = backoff_time * (2 ** (retry_count - 1))  # 指数退避
                    logger.warning(f"阿里云服务暂时不可用 (503)，第 {retry_count}/{max_retries} 次重试，等待 {wait_time} 秒...")
                    time.sleep(wait_time)
                else:
                    # 其他服务器错误
                    logger.error(f"获取慢查询明细记录服务器错误: {str(e)}")
                    return []
                
            except Exception as e:
                logger.error(f"获取慢查询明细记录失败: {str(e)}")
                return []
        
        # 达到最大重试次数后仍失败
        logger.error(f"获取慢查询明细记录失败: 已达到最大重试次数 ({max_retries})")
        return []


class InstanceSyncService:
    """实例同步服务，用于从阿里云同步RDS实例信息"""
    
    @staticmethod
    def sync_all_instances(user=None):
        """同步所有地域的RDS实例
        
        Args:
            user: 执行同步的用户
            
        Returns:
            同步结果统计
        """
        from .models import AliCloudRegion, RDSInstance, Database, DBAccount, OperationLog
        
        # 检查是否有配置地域
        regions = AliCloudRegion.objects.all()
        if not regions.exists():
            # 创建默认地域
            default_region = AliCloudRegion.objects.create(
                region_id=settings.ALIYUN_REGION_ID,
                region_name="默认地域"
            )
            regions = [default_region]
        
        # 记录操作日志
        log = OperationLog.objects.create(
            user_id=user.id if user else None,
            operation_type='sync_instances',
            operation_detail='同步RDS实例数据',
            operation_status=True
        )
        
        # 初始化计数器
        total_count = 0
        new_count = 0
        updated_count = 0
        failed_count = 0
        
        try:
            # 遍历所有地域
            for region in regions:
                logger.info(f"同步地域: {region.region_id}")
                
                # 创建RDS服务
                rds_service = AliCloudRDSService(region_id=region.region_id)
                
                # 获取实例列表
                response = rds_service.list_instances()
                instances = response.get('Items', {}).get('DBInstance', [])
                total_count += len(instances)
                
                # 遍历实例
                for instance_data in instances:
                    instance_id = instance_data.get('DBInstanceId')
                    logger.info(f"同步实例: {instance_id}")
                    try:
                        # 查找或创建实例
                        instance, created = RDSInstance.objects.update_or_create(
                            instance_id=instance_id,
                            defaults={
                                'instance_name': instance_data.get('DBInstanceDescription', ''),
                                'instance_type': instance_data.get('DBInstanceType', ''),
                                'instance_class': instance_data.get('DBInstanceClass', ''),
                                'engine': instance_data.get('Engine', ''),
                                'engine_version': instance_data.get('EngineVersion', ''),
                                'status': instance_data.get('DBInstanceStatus', ''),
                                'region_id': region.id,
                                'zone_id': instance_data.get('ZoneId', ''),
                                'connection_string': instance_data.get('ConnectionString', ''),
                                'port': instance_data.get('Port', 3306),
                                'vpc_id': instance_data.get('VpcId', ''),
                                'create_time': datetime.fromisoformat(instance_data.get('CreateTime', '').replace('Z', '+00:00')),
                                'expire_time': datetime.fromisoformat(instance_data.get('ExpireTime', '').replace('Z', '+00:00')) if instance_data.get('ExpireTime') else None,
                                'last_sync_time': timezone.now()
                            }
                        )
                        
                        if created:
                            new_count += 1
                            logger.info(f"创建新实例: {instance_id}")
                        else:
                            updated_count += 1
                            logger.info(f"更新实例: {instance_id}")
                        
                        # 同步数据库
                        try:
                            db_response = rds_service.get_instance_databases(instance_id)
                            databases = db_response.get('Databases', {}).get('Database', [])
                            for db_data in databases:
                                logger.info(f"同步数据库: {db_data.get('DBName')}")
                                Database.objects.update_or_create(
                                    instance_id=instance_id,
                                    name=db_data.get('DBName'),
                                    defaults={
                                        'character_set': db_data.get('CharacterSetName', ''),
                                        'description': db_data.get('DBDescription', '')
                                    }
                                )
                        except Exception as e:
                            logger.error(f"同步数据库失败: {instance_id}, 错误: {str(e)}")
                        
                        # 同步账号
                        try:
                            account_response = rds_service.get_instance_accounts(instance_id)
                            accounts = account_response.get('Accounts', {}).get('DBInstanceAccount', [])
                            for account_data in accounts:
                                DBAccount.objects.update_or_create(
                                    instance_id=instance_id,
                                    account_name=account_data.get('AccountName'),
                                    defaults={
                                        'account_type': account_data.get('AccountType', ''),
                                        'account_status': account_data.get('AccountStatus', ''),
                                        'description': account_data.get('AccountDescription', '')
                                    }
                                )
                        except Exception as e:
                            logger.error(f"同步账号失败: {instance_id}, 错误: {str(e)}")
                                                
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"同步实例失败: {instance_id}, 错误: {str(e)}")
            
            # 更新操作日志
            log.operation_detail = f"同步完成。共发现{total_count}个实例，新增{new_count}个，更新{updated_count}个，失败{failed_count}个。"
            log.save()
            
            return {
                'total': total_count,
                'new': new_count,
                'updated': updated_count,
                'failed': failed_count
            }
            
        except Exception as e:
            # 更新操作日志
            log.operation_status = False
            log.operation_detail = f"同步失败: {str(e)}"
            log.save()
            
            logger.error(f"同步所有实例失败: {str(e)}")
            return {'error': str(e)}
    
    @staticmethod
    def sync_instance_databases(instance_id, user=None):
        """同步指定实例的数据库
        
        Args:
            instance_id: 实例ID（字符串）
            user: 执行同步的用户
            
        Returns:
            同步结果统计
        """
        from .models import RDSInstance, Database, OperationLog, SlowQueryLog
        
        try:
            # 获取实例
            instance = RDSInstance.objects.get(instance_id=instance_id)
            
            # 记录操作日志
            log = OperationLog.objects.create(
                user_id=user.id if user else None,
                instance_string_id=instance.instance_id,
                operation_type='sync_databases',
                operation_detail=f'同步实例 {instance.instance_id} 的数据库',
                operation_status=True
            )
            
            # 初始化计数器
            total_count = 0
            new_count = 0
            updated_count = 0
            
            # 创建RDS服务
            rds_service = AliCloudRDSService(region_id=instance.region.region_id if instance.region else None)
            
            # 同步数据库
            db_response = rds_service.get_instance_databases(instance.instance_id)
            databases = db_response.get('Databases', {}).get('Database', [])
            total_count = len(databases)
            
            # 记录当前数据库
            current_db_names = set()
            
            for db_data in databases:
                db_name = db_data.get('DBName')
                current_db_names.add(db_name)
                
                db, created = Database.objects.update_or_create(
                    instance_id=instance.instance_id,
                    name=db_name,
                    defaults={
                        'character_set': db_data.get('CharacterSetName', ''),
                        'description': db_data.get('DBDescription', '')
                    }
                )
                
                if created:
                    new_count += 1
                    logger.info(f"创建新数据库: {db_name}")
                else:
                    updated_count += 1
                    logger.info(f"更新数据库: {db_name}")
            
            # 更新实例的最后同步时间
            instance.last_sync_time = timezone.now()
            instance.save(update_fields=['last_sync_time'])
            
            # 更新操作日志
            message = f"同步完成。共发现{total_count}个数据库，新增{new_count}个，更新{updated_count}个。"
            log.operation_detail = message
            log.save()
            
            return {
                'success': True,
                'total': total_count,
                'new': new_count,
                'updated': updated_count,
                'message': message
            }
            
        except RDSInstance.DoesNotExist:
            message = f"实例不存在 ID: {instance_id}"
            logger.error(message)
            return {'success': False, 'error': message}
        except Exception as e:
            message = f"同步数据库失败: {str(e)}"
            logger.error(message)
            
            # 更新操作日志
            if 'log' in locals():
                log.operation_status = False
                log.operation_detail = message
                log.save()
                
            return {'success': False, 'error': message}


class SlowQueryService:
    """慢查询服务，用于从阿里云同步慢查询日志并进行分析"""
    
    @staticmethod
    def extract_username(sql_text):
        """从SQL文本中提取用户名
        
        尝试从SQL文本中提取用户名，如果无法提取则返回默认值
        
        Args:
            sql_text: SQL文本
            
        Returns:
            提取到的用户名或默认值
        """
        try:
            if 'by ' in sql_text:
                # 尝试提取形如 "SQL executed by user[db]" 的用户名
                username_part = sql_text.split('by ')[1]
                if '[' in username_part:
                    return username_part.split('[')[0].strip()
                else:
                    return username_part.strip()
            
            # 如果没有找到匹配的模式，返回默认值
            return 'unknown'
        except Exception as e:
            logger.error(f"提取用户名失败: {str(e)}")
            return 'unknown'
    
    @staticmethod
    def sync_slow_queries(instance_id, start_time=None, end_time=None, user=None):
        """同步指定实例的慢查询日志统计
        
        Args:
            instance_id: 实例ID
            start_time: 开始时间
            end_time: 结束时间
            user: 执行同步的用户
            
        Returns:
            同步结果统计
        """
        from .models import RDSInstance, SlowQueryLog, OperationLog, Database
        from datetime import datetime, timedelta
        from django.utils import timezone
        import time
        import json
        
        try:
            # 获取实例
            instance = RDSInstance.objects.get(instance_id=instance_id)
            
            # 设置默认时间范围：最近7天
            if not start_time:
                start_time = timezone.now() - timedelta(days=7)
            if not end_time:
                end_time = timezone.now()
            
            # 记录操作日志
            log = OperationLog.objects.create(
                user_id=user.id if user else None,
                instance_string_id=instance.instance_id,
                operation_type='sync_slow_queries',
                operation_detail=f'同步实例 {instance.instance_id} 的慢查询日志统计',
                operation_status=True
            )
            
            # 初始化计数器
            total_count = 0
            new_count = 0
            
            # 创建RDS服务
            rds_service = AliCloudRDSService(region_id=instance.region.region_id if instance.region else None)
            
            # 转换为阿里云API需要的时间格式 yyyy-MM-ddZ
            start_time_str = start_time.astimezone(timezone.utc).strftime("%Y-%m-%dZ")
            end_time_str = end_time.astimezone(timezone.utc).strftime("%Y-%m-%dZ")
            
            logger.info(f"同步慢查询统计, 时间范围: {start_time_str} - {end_time_str}")
            
            # 初始化分页参数
            page_number = 1
            page_size = 100
            has_more = True
            max_retries = 5
            
            # 循环获取所有页的数据
            while has_more:
                retry_count = 0
                success = False
                
                # 重试机制，处理API限流
                while not success and retry_count < max_retries:
                    try:
                        logger.info(f"获取慢查询统计, 页码: {page_number}, 每页数量: {page_size}")
                        # 获取慢查询统计
                        response = rds_service.get_slow_query_logs(
                            instance_id=instance.instance_id,
                            start_time=start_time_str,
                            end_time=end_time_str,
                            page_number=page_number,
                            page_size=page_size
                        )
                        
                        success = True
                    except Exception as e:
                        retry_count += 1
                        error_msg = str(e)
                        if "Throttling" in error_msg:
                            logger.warning(f"API被限流，等待60秒后重试 ({retry_count}/{max_retries})")
                            time.sleep(60)  # 限流时等待60秒
                        else:
                            logger.error(f"获取慢查询统计失败: {error_msg}")
                            # 非限流错误直接抛出
                            raise
                
                if not success:
                    logger.error(f"达到最大重试次数，放弃当前页 {page_number}")
                    break
                
                # 解析响应
                slow_query_stats = response.get('Items', {}).get('SQLSlowLog', [])
                page_count = len(slow_query_stats)
                total_records = response.get('TotalRecordCount', 0)
                
                logger.info(f"获取到第{page_number}页数据，共{page_count}条记录，总记录数: {total_records}")
                
                # 遍历慢查询统计
                for stat in slow_query_stats:
                    try:
                        # 解析日期
                        create_time_str = stat.get('CreateTime', '')
                        try:
                            # 移除Z后缀并解析日期
                            timezone_date = datetime.strptime(create_time_str.replace('Z', ''), "%Y-%m-%d").date()
                        except ValueError:
                            logger.warning(f"无法解析统计日期: {create_time_str}，使用当前日期")
                            timezone_date = timezone.now().date()
                        
                        # 预处理SQL文本
                        sql_text = stat.get('SQLText', '')
                        
                        # 当DBName为空时，尝试从SQL中提取数据库名
                        database_name = stat.get('DBName', '')
                        if not database_name:
                            # 尝试从SQL中提取数据库名，支持多种常见格式的database.table模式
                            
                            # 通用正则表达式，匹配常见SQL语句中的database.table格式
                            # 支持SELECT ... FROM db.table, JOIN db.table, INSERT INTO db.table等
                            db_table_match = re.search(r'(?:FROM|JOIN|INTO|UPDATE)\s+([a-zA-Z0-9_]+)\.([a-zA-Z0-9_]+)', sql_text, re.IGNORECASE)
                            if db_table_match:
                                database_name = db_table_match.group(1)
                                table_name = db_table_match.group(2)
                                logger.info(f"从SQL中提取到数据库名: {database_name}, 表名: {table_name}")
                            else:
                                # 尝试匹配其他可能的格式
                                alt_match = re.search(r'`?([a-zA-Z0-9_]+)`?\.`?([a-zA-Z0-9_]+)`?', sql_text)
                                if alt_match:
                                    database_name = alt_match.group(1)
                                    table_name = alt_match.group(2)
                                    logger.info(f"从备选模式中提取到数据库名: {database_name}, 表名: {table_name}")
                                else:
                                    # 如果无法从SQL中提取，则使用默认值
                                    database_name = "unknown_db"
                                    logger.warning(f"DBName为空且无法从SQL中提取数据库名，使用默认值: {database_name}")
                        
                        # 如果数据库不存在于系统中，自动创建一个数据库记录
                        try:
                            db_exists = Database.objects.filter(
                                instance_id=instance.instance_id,
                                name=database_name
                            ).exists()
                            
                            if not db_exists:
                                # 为未知数据库或从SQL提取的数据库创建一个记录
                                description = ""
                                if database_name == "unknown_db":
                                    description = "由系统自动创建，API返回的慢查询数据中未提供数据库名称"
                                else:
                                    description = f"由系统基于慢查询SQL中的数据库引用自动创建: '{database_name}'"
                                
                                Database.objects.create(
                                    instance_id=instance.instance_id,
                                    name=database_name,
                                    character_set="utf8mb4",  # 默认字符集
                                    description=description
                                )
                                logger.info(f"自动创建数据库记录: {database_name} (实例: {instance.instance_id})")
                        except Exception as e:
                            logger.error(f"自动创建数据库记录失败: {str(e)}")
                        
                        # 创建或更新慢查询记录
                        slow_query, created = SlowQueryLog.objects.update_or_create(
                            instance_id=instance.instance_id,
                            sql_hash=stat.get('SQLHASH', ''),
                            timezone=timezone_date,
                            defaults={
                                'database_name': database_name,
                                'sql_count': int(stat.get('MySQLTotalExecutionCounts', 0)),
                                'parse_row_count': int(stat.get('ParseTotalRowCounts', 0)),
                                'return_row_count': int(stat.get('ReturnTotalRowCounts', 0)),
                                'max_exe_time': int(stat.get('MaxExecutionTimeMS', 0)),
                                'max_lock_time': int(stat.get('MaxLockTimeMS', 0)),
                                'sql_text': sql_text,
                                'risk_level': 0  # 默认风险等级，稍后更新
                            }
                        )
                        
                        if created:
                            new_count += 1
                            logger.info(f"创建新慢查询统计记录: {slow_query}")
                        
                        # 对慢查询进行分析和评分
                        analysis_result = SlowQueryService.analyze_slow_query(slow_query)
                        
                        # 更新风险等级
                        if analysis_result.get('success', False):
                            slow_query.risk_level = analysis_result.get('risk_level', 0)
                            slow_query.save(update_fields=['risk_level'])
                        
                    except Exception as e:
                        logger.error(f"处理慢查询统计记录失败: {str(e)}")
                        continue
                
                total_count += page_count
                
                # 检查是否还有更多数据
                if page_count < page_size or total_count >= total_records:
                    has_more = False
                else:
                    page_number += 1
                    # 在请求下一页之前稍作延迟，避免触发限流
                    time.sleep(1)
            
            # 更新操作日志
            message = f"同步完成。共发现{total_count}条慢查询统计，新增{new_count}条。"
            log.operation_detail = message
            log.save()
            
            return {
                'success': True,
                'total': total_count,
                'new': new_count,
                'message': message
            }
            
        except RDSInstance.DoesNotExist:
            message = f"实例不存在 ID: {instance_id}"
            logger.error(message)
            return {'success': False, 'error': message}
        except Exception as e:
            message = f"同步慢查询统计失败: {str(e)}"
            logger.error(message)
            
            # 更新操作日志
            if 'log' in locals():
                log.operation_status = False
                log.operation_detail = message
                log.save()
                
            return {'success': False, 'error': message}
    
    @staticmethod
    def analyze_slow_query(slow_query):
        """分析慢查询并进行评分
        
        Args:
            slow_query: SlowQueryLog对象
            
        Returns:
            分析结果
        """
        from .models import SlowQueryAnalysis, SlowQueryScoreConfig
        import re
        import math
        
        # 初始化分析结果
        analysis_result = []
        optimization_suggestion = []
        
        # 提取SQL信息
        sql_text = slow_query.sql_text.lower() if slow_query.sql_text else ""
        
        # 检查是否包含EXPLAIN信息，如果有，则提取执行计划信息
        explain_match = re.search(r'explain\s+(.*)', sql_text)
        if explain_match:
            sql_text = explain_match.group(1)
            
        # 获取慢查询类型的激活评分配置
        try:
            config = SlowQueryScoreConfig.get_active_config(config_type='slowlog')
            logger.info(f"使用慢查询评分配置: {config.name}")
        except Exception as e:
            logger.error(f"获取慢查询评分配置失败，使用默认值: {str(e)}")
            # 使用默认配置
            config = None
        
        # 风险评分标准参数
        # 1. 查询次数
        query_count = slow_query.sql_count
        query_count_min = config.query_count_min if config else 5
        query_count_max = config.query_count_max if config else 500
        query_count_weight = config.query_count_weight if config else 0.4
        query_count_curve = config.query_count_curve if config else 'linear'
        
        # 2. 单次查询时间(毫秒)
        query_time = slow_query.max_exe_time
        query_time_min = config.query_time_min if config else 0
        query_time_max = config.query_time_max if config else 10000  # 10秒
        query_time_weight = config.query_time_weight if config else 0.3
        query_time_curve = config.query_time_curve if config else 'sine'
        
        # 3. 单次扫描行数
        scan_rows = slow_query.parse_row_count
        scan_rows_min = config.scan_rows_min if config else 1
        scan_rows_max = config.scan_rows_max if config else 1000000  # 100万行
        scan_rows_weight = config.scan_rows_weight if config else 0.2
        scan_rows_curve = config.scan_rows_curve if config else 'polynomial'
        
        # 4. 单次影响(返回)行数
        affected_rows = slow_query.return_row_count
        affected_rows_min = config.affected_rows_min if config else 0
        affected_rows_max = config.affected_rows_max if config else 500
        affected_rows_weight = config.affected_rows_weight if config else 0.05
        affected_rows_curve = config.affected_rows_curve if config else 'linear'
        
        # 5. 锁等待时间(毫秒)
        lock_time = slow_query.max_lock_time
        lock_time_min = config.lock_time_min if config else 0
        lock_time_max = config.lock_time_max if config else 1000  # 1秒
        lock_time_weight = config.lock_time_weight if config else 0.05
        lock_time_curve = config.lock_time_curve if config else 'exponential'
        
        # 计算各项得分比例(0-1之间)
        # 根据曲线类型计算得分
        def calculate_score(value, min_val, max_val, curve_type, config=None):
            # 超出范围处理
            if value <= min_val:
                return 0
            if value >= max_val:
                return 1
                
            # 标准化到0-1区间
            normalized = (value - min_val) / (max_val - min_val)
            
            # 根据曲线类型计算
            if curve_type == 'linear':
                # 线性曲线: Y = X
                return normalized
            elif curve_type == 'exponential':
                # 指数曲线: Y = 2^(X * log₂(100)) / 100
                return pow(2, normalized * math.log2(100)) / 100
            elif curve_type == 'logarithmic':
                # 对数曲线: Y = log₁₀(9*X+1)
                return math.log10(9 * normalized + 1)
            elif curve_type == 'sine':
                # 正弦曲线: Y = sin(X * π/2)
                return math.sin(normalized * math.pi / 2)
            elif curve_type == 'polynomial':
                # 多项式曲线: Y = a + b·X + c·X² + d·X³ + e·X⁴ + f·X⁵
                # 获取系数
                if config:
                    a, b, c, d, e, f = config.polynomial_a, config.polynomial_b, config.polynomial_c, config.polynomial_d, config.polynomial_e, config.polynomial_f
                else:
                    a, b, c, d, e, f = 0.0, 0.02, 0.001, 0.0005, 0.00001, 0.000001
                
                # 这里将范围缩小到0-20，适合多项式计算
                x = normalized * 20
                return min(1.0, a + b*x + c*x**2 + d*x**3 + e*x**4 + f*x**5)
            else:
                # 默认使用线性曲线
                return normalized
        
        # 计算各项得分
        query_count_score = calculate_score(query_count, query_count_min, query_count_max, query_count_curve, config)
        query_time_score = calculate_score(query_time, query_time_min, query_time_max, query_time_curve, config)
        scan_rows_score = calculate_score(scan_rows, scan_rows_min, scan_rows_max, scan_rows_curve, config)
        affected_rows_score = calculate_score(affected_rows, affected_rows_min, affected_rows_max, affected_rows_curve, config)
        lock_time_score = calculate_score(lock_time, lock_time_min, lock_time_max, lock_time_curve, config)
        
        # 记录各项得分
        logger.debug(f"慢查询评分明细: ID={slow_query.id}, 查询次数={query_count_score:.2f}, 查询时间={query_time_score:.2f}, " +
                    f"扫描行数={scan_rows_score:.2f}, 影响行数={affected_rows_score:.2f}, 锁等待时间={lock_time_score:.2f}")
        
        # 计算加权总分
        total_score = (query_count_score * query_count_weight + 
                    query_time_score * query_time_weight + 
                    scan_rows_score * scan_rows_weight + 
                    affected_rows_score * affected_rows_weight + 
                    lock_time_score * lock_time_weight)
        
        # 总分(0-1)换算为风险级别(0-100)
        risk_score = int(total_score * 100)
        
        # 分析结果描述及优化建议
        if query_count > query_count_min:
            analysis_result.append(f"查询次数较多（{query_count}次）")
            if query_count > 100:
                optimization_suggestion.append("考虑使用缓存或优化应用逻辑减少查询次数")
        
        if query_time > 1000:  # 超过1秒
            analysis_result.append(f"查询耗时较长（{query_time/1000:.2f}秒）")
            optimization_suggestion.append("优化查询语句或添加适当的索引")
        
        if scan_rows > 10000:
            analysis_result.append(f"扫描行数较大（{scan_rows}行）")
            optimization_suggestion.append("检查WHERE条件，考虑增加或优化索引")
        
        if lock_time > 100:  # 超过100毫秒
            analysis_result.append(f"锁等待时间较长（{lock_time}毫秒）")
            optimization_suggestion.append("检查事务设计，避免长时间持有锁")
        
        # 检查SQL文本中的风险模式
        # 1. 检查是否有全表扫描
        table_scan = False
        if re.search(r'select\s+.*\s+from\s+\w+\s+(where|order|group|having|limit)', sql_text) and not re.search(r'where', sql_text):
            table_scan = True
            analysis_result.append("执行全表扫描")
            optimization_suggestion.append("添加WHERE条件限制查询范围")
        
        # 2. 检查JOIN操作
        if re.search(r'join', sql_text):
            if not re.search(r'inner join|left join|right join', sql_text):
                analysis_result.append("使用了隐式JOIN")
                optimization_suggestion.append("使用显式JOIN语法提高可读性")
            
            # 检查多表JOIN
            join_count = len(re.findall(r'join', sql_text))
            if join_count > 3:
                analysis_result.append(f"多表JOIN操作（{join_count}个表）")
                optimization_suggestion.append("考虑拆分查询或使用临时表")
        
        # 3. 检查是否使用了函数在索引字段上
        if re.search(r'(where|on)\s+\w+\s*\(\s*\w+\s*\)', sql_text):
            analysis_result.append("在WHERE/ON子句中对字段使用了函数")
            optimization_suggestion.append("避免在索引字段上使用函数，会导致索引失效")
        
        # 4. 检查是否使用了NOT IN, NOT EXISTS
        if re.search(r'not\s+in|not\s+exists', sql_text):
            analysis_result.append("使用了NOT IN或NOT EXISTS")
            optimization_suggestion.append("考虑使用LEFT JOIN ... IS NULL替代")
        
        # 5. 检查是否使用了LIKE '%...%'
        if re.search(r'like\s+[\'"]%.*%[\'"]', sql_text):
            analysis_result.append("使用了两边都有%的LIKE模糊查询")
            optimization_suggestion.append("避免使用'%'开头的LIKE，会导致索引失效")
        
        # 6. 检查ORDER BY和GROUP BY
        if re.search(r'order\s+by\s+\w+', sql_text) and scan_rows > 10000:
            analysis_result.append("大数据量排序操作")
            optimization_suggestion.append("考虑为ORDER BY字段创建索引")
        
        if re.search(r'group\s+by', sql_text) and scan_rows > 10000:
            analysis_result.append("大数据量分组操作")
            optimization_suggestion.append("考虑为GROUP BY字段创建索引")
        
        # 设置风险等级
        if risk_score >= 70:
            risk_level = 2  # 高风险
        elif risk_score >= 30:
            risk_level = 1  # 中风险
        else:
            risk_level = 0  # 低风险
        
        # 更新慢查询风险等级
        slow_query.risk_level = risk_level
        slow_query.save(update_fields=['risk_level'])
        
        # 创建或更新分析结果
        analysis, created = SlowQueryAnalysis.objects.update_or_create(
            sql_hash=slow_query.sql_hash,
            instance_id=slow_query.instance_id,
            timezone=slow_query.timezone,
            defaults={
                'table_scans': table_scan,
                'no_indexes': 'no suitable index found' in sql_text or 'no matching index' in sql_text,
                'poor_indexes': 'using temporary' in sql_text or 'using filesort' in sql_text,
                'temporary_tables': 'using temporary' in sql_text,
                'filesort': 'using filesort' in sql_text,
                'analysis_result': '\n'.join(analysis_result),
                'optimization_suggestion': '\n'.join(optimization_suggestion),
                'risk_score': risk_score,
                'query_count_score': round(query_count_score * 100),
                'query_time_score': round(query_time_score * 100),
                'scan_rows_score': round(scan_rows_score * 100),
                'affected_rows_score': round(affected_rows_score * 100),
                'lock_time_score': round(lock_time_score * 100)
            }
        )
        
        # 返回结果
        return {
            'success': True,
            'risk_level': risk_level,
            'risk_score': risk_score,
            'query_count_score': round(query_count_score * 100),
            'query_time_score': round(query_time_score * 100),
            'scan_rows_score': round(scan_rows_score * 100),
            'affected_rows_score': round(affected_rows_score * 100),
            'lock_time_score': round(lock_time_score * 100),
            'analysis': analysis
        } 