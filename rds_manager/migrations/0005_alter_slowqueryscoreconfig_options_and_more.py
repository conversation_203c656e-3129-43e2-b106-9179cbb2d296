# Generated by Django 4.2 on 2025-07-17 11:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rds_manager', '0004_rdsinstancespec'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='slowqueryscoreconfig',
            options={'ordering': ['-created_at'], 'verbose_name': '评分配置', 'verbose_name_plural': '评分配置'},
        ),
        migrations.AddField(
            model_name='slowqueryscoreconfig',
            name='config_type',
            field=models.CharField(choices=[('slowlog', '慢查询'), ('sqltemplate', 'SQL模板')], default='slowlog', max_length=16, verbose_name='配置类型'),
        ),
    ]
