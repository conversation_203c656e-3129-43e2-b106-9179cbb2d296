{"asctime": "2025-07-17 11:31:56,183", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:32:07,440", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/models.py changed, reloading."}
{"asctime": "2025-07-17 11:32:08,283", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:32:33,259", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/models.py changed, reloading."}
{"asctime": "2025-07-17 11:32:34,161", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:33:00,086", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/models.py changed, reloading."}
{"asctime": "2025-07-17 11:33:01,104", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:33:37,416", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/api/services.py changed, reloading."}
{"asctime": "2025-07-17 11:33:38,215", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:33:50,662", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/services.py changed, reloading."}
{"asctime": "2025-07-17 11:33:51,400", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:34:01,847", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:34:02,734", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:34:20,423", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:34:21,161", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:34:31,510", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:34:32,240", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:34:47,739", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:34:48,460", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:35:00,857", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:35:01,575", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:35:31,617", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:35:32,318", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:36:09,590", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/models.py changed, reloading."}
{"asctime": "2025-07-17 11:36:10,665", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:46:05,488", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:46:17,156", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:46:32,262", "levelname": "WARNING", "name": "django.request", "message": "Not Found: /rds/score-config/", "status_code": 404, "request": "<WSGIRequest: GET '/rds/score-config/'>"}
{"asctime": "2025-07-17 11:46:32,264", "levelname": "WARNING", "name": "django.server", "message": "\"GET /rds/score-config/ HTTP/1.1\" 404 8108", "request": "<socket.socket fd=28, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 8001), raddr=('127.0.0.1', 54544)>", "server_time": "17/Jul/2025 11:46:32", "status_code": 404}
