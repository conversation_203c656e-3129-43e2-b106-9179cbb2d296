{"asctime": "2025-07-17 11:31:56,183", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:32:07,440", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/models.py changed, reloading."}
{"asctime": "2025-07-17 11:32:08,283", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:32:33,259", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/models.py changed, reloading."}
{"asctime": "2025-07-17 11:32:34,161", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:33:00,086", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/models.py changed, reloading."}
{"asctime": "2025-07-17 11:33:01,104", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:33:37,416", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/api/services.py changed, reloading."}
{"asctime": "2025-07-17 11:33:38,215", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:33:50,662", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/services.py changed, reloading."}
{"asctime": "2025-07-17 11:33:51,400", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:34:01,847", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:34:02,734", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:34:20,423", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:34:21,161", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:34:31,510", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:34:32,240", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:34:47,739", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:34:48,460", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:35:00,857", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:35:01,575", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:35:31,617", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/views.py changed, reloading."}
{"asctime": "2025-07-17 11:35:32,318", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
{"asctime": "2025-07-17 11:36:09,590", "levelname": "INFO", "name": "django.utils.autoreload", "message": "/Users/<USER>/Timevale/python/dba_0331/rds_manager/models.py changed, reloading."}
{"asctime": "2025-07-17 11:36:10,665", "levelname": "INFO", "name": "django.utils.autoreload", "message": "Watching for file changes with StatReloader"}
